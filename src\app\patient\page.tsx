'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useRealtimeSubscription } from '@/hooks/useRealtimeSubscription';

interface Appointment {
  id: string;
  appointment_date: string;
  appointment_time: string;
  treatment_type: string;
  status: string;
  notes?: string;
  doctor_name?: string;
  clinic_name?: string;
}

interface Treatment {
  id: string;
  treatment_date: string;
  treatment_type: string;
  description: string;
  cost: number;
}

export default function PatientPortalPage() {
  const [activeTab, setActiveTab] = useState('appointments');

  const { user, isAuthenticated, logout } = useAuth();
  const router = useRouter();

  // Real-time data subscriptions for patient
  const { data: appointments, loading: appointmentsLoading, error: appointmentsError } = useRealtimeSubscription<Appointment>({
    table: 'appointments',
    enabled: isAuthenticated && user?.role === 'patient'
  });

  const { data: treatments, loading: treatmentsLoading, error: treatmentsError } = useRealtimeSubscription<Treatment>({
    table: 'treatments',
    enabled: isAuthenticated && user?.role === 'patient'
  });

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirectTo=/patient');
      return;
    }

    if (user?.role !== 'patient') {
      // If user is not a patient, redirect to appropriate dashboard
      const redirectUrl = user?.role === 'super_admin' ? '/admin' : 
                         user?.role === 'doctor' ? '/doctor/dashboard' : 
                         '/auth/login';
      router.push(redirectUrl);
      return;
    }

    // Real-time data is automatically fetched by hooks
    console.log('✅ Patient portal using real-time data subscriptions');
  }, [isAuthenticated, user, router]);

  const handleLogout = async () => {
    await logout();
    router.push('/');
  };

  if (!isAuthenticated || user?.role !== 'patient') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
          <p className="text-gray-600 mb-6">You need to be logged in as a patient to access this page.</p>
          <Link href="/auth/login">
            <Button>Go to Login</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-blue-600">DentoPro</h1>
              <span className="ml-2 text-sm text-gray-500">Patient Portal</span>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">Welcome, {user?.email}</span>
              <Button variant="outline" onClick={handleLogout}>
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Your Health Dashboard
          </h2>
          <p className="text-lg text-gray-600">
            View your appointments, treatment history, and manage your dental care
          </p>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Link href="/patient/book-appointment">
            <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow cursor-pointer">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Book Appointment</h3>
              <p className="text-gray-600">Schedule your next dental visit</p>
            </div>
          </Link>

          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Treatment History</h3>
            <p className="text-gray-600">{treatments.length} treatments recorded</p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
              <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Upcoming Appointments</h3>
            <p className="text-gray-600">{appointments.filter(apt => new Date(apt.appointment_date) > new Date()).length} scheduled</p>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-md">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('appointments')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'appointments'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Appointments
              </button>
              <button
                onClick={() => setActiveTab('treatments')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'treatments'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Treatment History
              </button>
            </nav>
          </div>

          <div className="p-6">
            {(appointmentsLoading || treatmentsLoading) ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Loading...</p>
              </div>
            ) : (
              <>
                {activeTab === 'appointments' && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Your Appointments</h3>
                    {appointments.length === 0 ? (
                      <div className="text-center py-8">
                        <p className="text-gray-600 mb-4">No appointments found.</p>
                        <Link href="/patient/book-appointment">
                          <Button>Book Your First Appointment</Button>
                        </Link>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {appointments.map((appointment) => (
                          <div key={appointment.id} className="border border-gray-200 rounded-lg p-4">
                            <div className="flex justify-between items-start">
                              <div>
                                <h4 className="font-semibold text-gray-900">
                                  {appointment.treatment_type || 'General Appointment'}
                                </h4>
                                <p className="text-sm text-gray-600">
                                  {new Date(appointment.appointment_date).toLocaleDateString()} at {appointment.appointment_time}
                                </p>
                                {appointment.doctor_name && (
                                  <p className="text-sm text-gray-600">Dr. {appointment.doctor_name}</p>
                                )}
                                {appointment.notes && (
                                  <p className="text-sm text-gray-600 mt-2">{appointment.notes}</p>
                                )}
                              </div>
                              <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                                appointment.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                                appointment.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-gray-100 text-gray-800'
                              }`}>
                                {appointment.status}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                {activeTab === 'treatments' && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Treatment History</h3>
                    {treatments.length === 0 ? (
                      <div className="text-center py-8">
                        <p className="text-gray-600">No treatments recorded yet.</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {treatments.map((treatment) => (
                          <div key={treatment.id} className="border border-gray-200 rounded-lg p-4">
                            <div className="flex justify-between items-start">
                              <div>
                                <h4 className="font-semibold text-gray-900">{treatment.treatment_type}</h4>
                                <p className="text-sm text-gray-600">
                                  {new Date(treatment.treatment_date).toLocaleDateString()}
                                </p>
                                <p className="text-sm text-gray-600 mt-2">{treatment.description}</p>
                              </div>
                              <div className="text-right">
                                <p className="font-semibold text-gray-900">${treatment.cost}</p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
