-- =============================================
-- DENTOPRO SAAS - DATABASE STATUS VERIFICATION
-- =============================================
-- Run this in your Supabase SQL Editor to check current database status

-- =============================================
-- 1. CHECK DOCTORS TABLE STRUCTURE
-- =============================================

SELECT 
    'DOCTORS TABLE STRUCTURE' as check_type,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'doctors' 
ORDER BY ordinal_position;

-- =============================================
-- 2. CHECK FOR CLINIC_SLUG COLUMN
-- =============================================

SELECT 
    'CLINIC_SLUG STATUS' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'doctors' AND column_name = 'clinic_slug'
        ) THEN '✅ clinic_slug column EXISTS'
        ELSE '❌ clinic_slug column MISSING'
    END as status;

-- =============================================
-- 3. CHECK UNIQUE CONSTRAINTS
-- =============================================

SELECT 
    'UNIQUE CONSTRAINTS' as check_type,
    tc.constraint_name, 
    tc.constraint_type,
    kcu.column_name
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu 
    ON tc.constraint_name = kcu.constraint_name
WHERE tc.table_name = 'doctors' 
    AND tc.constraint_type IN ('UNIQUE', 'PRIMARY KEY');

-- =============================================
-- 4. CHECK FUNCTIONS
-- =============================================

SELECT 
    'FUNCTIONS STATUS' as check_type,
    routine_name,
    routine_type,
    CASE 
        WHEN routine_name LIKE '%clinic_slug%' THEN '✅ SLUG FUNCTION EXISTS'
        ELSE 'FUNCTION EXISTS'
    END as status
FROM information_schema.routines 
WHERE routine_name IN (
    'generate_unique_clinic_slug',
    'auto_generate_clinic_slug',
    'check_duplicate_email',
    'check_duplicate_license',
    'find_similar_doctors',
    'find_similar_clinics'
);

-- =============================================
-- 5. CHECK TRIGGERS
-- =============================================

SELECT 
    'TRIGGERS STATUS' as check_type,
    trigger_name,
    event_manipulation,
    action_timing,
    '✅ TRIGGER EXISTS' as status
FROM information_schema.triggers 
WHERE trigger_name LIKE '%clinic_slug%';

-- =============================================
-- 6. CHECK CURRENT DOCTORS DATA
-- =============================================

SELECT 
    'CURRENT DOCTORS DATA' as check_type,
    COUNT(*) as total_doctors,
    COUNT(CASE WHEN clinic_slug IS NOT NULL THEN 1 END) as doctors_with_slugs,
    COUNT(DISTINCT clinic_slug) as unique_slugs
FROM doctors;

-- =============================================
-- 7. SAMPLE DOCTORS WITH SLUGS
-- =============================================

SELECT 
    'SAMPLE DOCTOR SLUGS' as check_type,
    full_name,
    clinic_name,
    clinic_slug,
    'http://localhost:3000/book/' || COALESCE(clinic_slug, 'NO-SLUG') as booking_url
FROM doctors 
LIMIT 3;

-- =============================================
-- 8. CHECK CLINIC_SETTINGS ENHANCEMENTS
-- =============================================

SELECT 
    'CLINIC_SETTINGS STRUCTURE' as check_type,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'clinic_settings' 
ORDER BY ordinal_position;

-- =============================================
-- 9. OVERALL STATUS SUMMARY
-- =============================================

SELECT 
    'OVERALL STATUS' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'doctors' AND column_name = 'clinic_slug'
        ) AND EXISTS (
            SELECT 1 FROM information_schema.routines 
            WHERE routine_name = 'generate_unique_clinic_slug'
        ) AND EXISTS (
            SELECT 1 FROM information_schema.triggers 
            WHERE trigger_name LIKE '%clinic_slug%'
        ) THEN '🎉 ALL UPDATES APPLIED SUCCESSFULLY!'
        ELSE '⚠️ UPDATES NEEDED - Run database-updates-for-validation.sql'
    END as status;

-- =============================================
-- 10. NEXT STEPS
-- =============================================

SELECT 
    'NEXT STEPS' as info_type,
    CASE 
        WHEN NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'doctors' AND column_name = 'clinic_slug'
        ) THEN '1. Run database-updates-for-validation.sql in Supabase SQL Editor'
        WHEN EXISTS (
            SELECT 1 FROM doctors WHERE clinic_slug IS NULL
        ) THEN '2. Some doctors missing slugs - run UPDATE query'
        ELSE '3. ✅ Ready to test validation and public booking!'
    END as action_required;
