import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          },
        },
      }
    );

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Verify user is a patient
    const { data: patient, error: patientError } = await supabase
      .from('patients')
      .select('id, doctor_id')
      .eq('id', user.id)
      .single();

    if (patientError || !patient) {
      return NextResponse.json(
        { error: 'Patient not found' },
        { status: 404 }
      );
    }

    // Get patient's treatments
    const { data: treatments, error: treatmentsError } = await supabase
      .from('treatments')
      .select(`
        id,
        treatment_date,
        treatment_type,
        description,
        cost,
        status,
        created_at
      `)
      .eq('patient_id', patient.id)
      .order('treatment_date', { ascending: false });

    if (treatmentsError) {
      console.error('Error fetching treatments:', treatmentsError);
      return NextResponse.json(
        { error: 'Failed to fetch treatments' },
        { status: 500 }
      );
    }

    return NextResponse.json(treatments || []);

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
