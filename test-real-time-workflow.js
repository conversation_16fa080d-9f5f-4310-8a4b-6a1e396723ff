// Test bidirectional real-time workflow between Supabase Dashboard and Application
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('🔄 Testing REAL-TIME BIDIRECTIONAL WORKFLOW...\n');
console.log('📊 Your Supabase Dashboard: https://supabase.com/dashboard/project/mjurshygvtfntjipemin\n');

const supabase = createClient(supabaseUrl, supabaseKey);

async function testBidirectionalWorkflow() {
  try {
    console.log('🧪 TESTING WORKFLOW BETWEEN:');
    console.log('   📱 Your Application ↔ 🗄️ Your Supabase Dashboard\n');

    // Test 1: Check current data state
    console.log('1️⃣ CHECKING CURRENT DATA STATE...\n');
    
    const { data: doctors, count: doctorCount } = await supabase
      .from('doctors')
      .select('*', { count: 'exact' });
    
    const { data: patients, count: patientCount } = await supabase
      .from('patients')
      .select('*', { count: 'exact' });
    
    const { data: appointments, count: appointmentCount } = await supabase
      .from('appointments')
      .select('*', { count: 'exact' });

    console.log(`👨‍⚕️ Doctors: ${doctorCount} records`);
    console.log(`👥 Patients: ${patientCount} records`);
    console.log(`📅 Appointments: ${appointmentCount} records\n`);

    if (doctorCount === 0 && patientCount === 0 && appointmentCount === 0) {
      console.log('✅ DATABASE IS CLEAN - Ready for real user data!\n');
    } else {
      console.log('📋 EXISTING DATA FOUND:');
      doctors?.forEach(doctor => {
        console.log(`   👨‍⚕️ ${doctor.full_name} (${doctor.email}) - ${doctor.clinic_name}`);
      });
      patients?.forEach(patient => {
        console.log(`   👤 ${patient.full_name} (${patient.email})`);
      });
      console.log('');
    }

    // Test 2: Real-time subscription test
    console.log('2️⃣ TESTING REAL-TIME SUBSCRIPTIONS...\n');

    let updateDetected = false;

    // Set up real-time listener for doctors table
    const doctorChannel = supabase
      .channel('doctor-changes')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'doctors' }, 
        (payload) => {
          console.log('🔄 REAL-TIME UPDATE DETECTED in doctors table:');
          console.log('   Event:', payload.eventType);
          console.log('   Data:', payload.new || payload.old);
          updateDetected = true;
        }
      )
      .subscribe();

    // Set up real-time listener for patients table
    const patientChannel = supabase
      .channel('patient-changes')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'patients' }, 
        (payload) => {
          console.log('🔄 REAL-TIME UPDATE DETECTED in patients table:');
          console.log('   Event:', payload.eventType);
          console.log('   Data:', payload.new || payload.old);
          updateDetected = true;
        }
      )
      .subscribe();

    console.log('✅ Real-time listeners established for doctors and patients tables');
    console.log('✅ Ready to detect changes from both your app and Supabase dashboard\n');

    // Test 3: Workflow instructions
    console.log('3️⃣ WORKFLOW TESTING INSTRUCTIONS:\n');
    
    console.log('🧑‍⚕️ TO TEST DOCTOR REGISTRATION WORKFLOW:');
    console.log('   1. Open http://localhost:3000 in incognito browser');
    console.log('   2. Click "Doctor Login" and use NEW Google account');
    console.log('   3. Complete registration form');
    console.log('   4. Watch this console for real-time updates');
    console.log('   5. Check your Supabase dashboard - new doctor should appear\n');

    console.log('👥 TO TEST COMPLETE PATIENT BOOKING WORKFLOW:');
    console.log('   1. Open another incognito window → http://localhost:3000');
    console.log('   2. Click "Book Appointment" (don\'t login)');
    console.log('   3. STEP 1: Select a service (e.g., General Consultation)');
    console.log('   4. STEP 2: Choose date and available time slot');
    console.log('   5. STEP 3: Enter patient details (name, phone, email)');
    console.log('   6. STEP 4: Review and confirm appointment');
    console.log('   7. Watch this console for real-time updates');
    console.log('   8. Check your Supabase dashboard - new patient + appointment should appear');
    console.log('   9. Test returning patient: use same phone/email for next booking\n');

    console.log('🔄 TO TEST SUPABASE → APP WORKFLOW:');
    console.log('   1. Go to your Supabase dashboard');
    console.log('   2. Manually add/edit data in doctors or patients table');
    console.log('   3. Watch this console for real-time updates');
    console.log('   4. Refresh your application - changes should be visible\n');

    console.log('📊 TO TEST SUPER ADMIN DASHBOARD:');
    console.log('   1. Access /admin in your application');
    console.log('   2. Verify it shows REAL data counts (not mock data)');
    console.log('   3. Add new data and see counts update in real-time\n');

    console.log('🔄 REAL-TIME FRONTEND VERIFICATION:');
    console.log('   1. Open browser console (F12) while using the app');
    console.log('   2. Look for "Real-time change:" messages');
    console.log('   3. Add data in Supabase dashboard');
    console.log('   4. Watch app update WITHOUT page refresh');
    console.log('   5. Check for "Successfully subscribed to [table] changes" messages\n');

    // Keep the script running to monitor real-time updates
    console.log('🔍 MONITORING REAL-TIME UPDATES...');
    console.log('   (This script will run for 60 seconds to detect changes)');
    console.log('   (Press Ctrl+C to stop early)\n');

    // Wait for 60 seconds to monitor updates
    await new Promise(resolve => setTimeout(resolve, 60000));

    // Clean up
    supabase.removeChannel(doctorChannel);
    supabase.removeChannel(patientChannel);

    if (updateDetected) {
      console.log('🎉 SUCCESS: Real-time updates detected!');
    } else {
      console.log('ℹ️  No updates detected during monitoring period');
      console.log('   This is normal if no changes were made');
    }

    console.log('\n✅ WORKFLOW TEST COMPLETE!');
    console.log('🔗 Your application is connected to REAL Supabase data');

  } catch (error) {
    console.error('❌ Workflow test failed:', error);
  }
}

testBidirectionalWorkflow();
