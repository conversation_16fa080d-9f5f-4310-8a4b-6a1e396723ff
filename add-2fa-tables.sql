-- Additional tables for Two-Factor Authentication
-- Run this in Supabase SQL Editor after the main setup

-- Create two_factor_codes table for temporary verification codes
CREATE TABLE IF NOT EXISTS two_factor_codes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    phone_number VARCHAR(20) NOT NULL,
    verification_code VARCHAR(6) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_verified BOOLEAN DEFAULT false,
    verified_at TIMESTAMP WITH TIME ZONE,
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_2fa_settings table for 2FA configuration
CREATE TABLE IF NOT EXISTS user_2fa_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL UNIQUE,
    phone_number VARCHAR(20) NOT NULL,
    is_enabled BOOLEAN DEFAULT false,
    backup_codes TEXT[],
    enabled_at TIMESTAMP WITH TIME ZONE,
    disabled_at TIMESTAMP WITH TIME ZONE,
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create oauth_accounts table for OAuth integrations
CREATE TABLE IF NOT EXISTS oauth_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    provider VARCHAR(50) NOT NULL,
    provider_account_id VARCHAR(255) NOT NULL,
    provider_account_email VARCHAR(255),
    access_token TEXT,
    refresh_token TEXT,
    expires_at TIMESTAMP WITH TIME ZONE,
    token_type VARCHAR(50),
    scope TEXT,
    id_token TEXT,
    session_state TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(provider, provider_account_id)
);

-- Create login_attempts table for security monitoring
CREATE TABLE IF NOT EXISTS login_attempts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    success BOOLEAN NOT NULL,
    failure_reason VARCHAR(255),
    requires_2fa BOOLEAN DEFAULT false,
    two_fa_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE two_factor_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_2fa_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE oauth_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE login_attempts ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for 2FA tables
CREATE POLICY "Users can manage their own 2FA codes" ON two_factor_codes
    FOR ALL TO authenticated
    USING (user_id = auth.uid());

CREATE POLICY "Users can manage their own 2FA settings" ON user_2fa_settings
    FOR ALL TO authenticated
    USING (user_id = auth.uid());

CREATE POLICY "Users can manage their own OAuth accounts" ON oauth_accounts
    FOR ALL TO authenticated
    USING (user_id = auth.uid());

CREATE POLICY "Super admins can view all login attempts" ON login_attempts
    FOR SELECT TO authenticated
    USING (auth.jwt() ->> 'role' = 'super_admin');

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_two_factor_codes_user_id ON two_factor_codes(user_id);
CREATE INDEX IF NOT EXISTS idx_two_factor_codes_expires_at ON two_factor_codes(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_2fa_settings_user_id ON user_2fa_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_oauth_accounts_user_id ON oauth_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_oauth_accounts_provider ON oauth_accounts(provider, provider_account_id);
CREATE INDEX IF NOT EXISTS idx_login_attempts_email ON login_attempts(email);
CREATE INDEX IF NOT EXISTS idx_login_attempts_ip ON login_attempts(ip_address);
CREATE INDEX IF NOT EXISTS idx_login_attempts_created_at ON login_attempts(created_at);

-- Create function to clean up expired 2FA codes
CREATE OR REPLACE FUNCTION cleanup_expired_2fa_codes()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    DELETE FROM two_factor_codes 
    WHERE expires_at < NOW() - INTERVAL '1 hour';
END;
$$;

-- Create function to log login attempts
CREATE OR REPLACE FUNCTION log_login_attempt(
    p_email VARCHAR(255),
    p_ip_address INET,
    p_user_agent TEXT,
    p_success BOOLEAN,
    p_failure_reason VARCHAR(255) DEFAULT NULL,
    p_requires_2fa BOOLEAN DEFAULT FALSE,
    p_two_fa_verified BOOLEAN DEFAULT FALSE
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    attempt_id UUID;
BEGIN
    INSERT INTO login_attempts (
        email,
        ip_address,
        user_agent,
        success,
        failure_reason,
        requires_2fa,
        two_fa_verified
    ) VALUES (
        p_email,
        p_ip_address,
        p_user_agent,
        p_success,
        p_failure_reason,
        p_requires_2fa,
        p_two_fa_verified
    ) RETURNING id INTO attempt_id;
    
    RETURN attempt_id;
END;
$$;

-- Create function to check if user has 2FA enabled
CREATE OR REPLACE FUNCTION user_has_2fa_enabled(p_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    has_2fa BOOLEAN := FALSE;
BEGIN
    SELECT is_enabled INTO has_2fa
    FROM user_2fa_settings
    WHERE user_id = p_user_id;
    
    RETURN COALESCE(has_2fa, FALSE);
END;
$$;

-- Insert subscription plans
INSERT INTO subscription_plans (id, name, price, billing_cycle, features, is_active, created_at) VALUES
(
  'plan_basic',
  'Basic Plan',
  9.00,
  'monthly',
  ARRAY[
    'Up to 100 patients',
    'Basic appointment scheduling',
    'Patient records management',
    'Email support',
    'Mobile app access',
    'Basic reporting'
  ],
  true,
  NOW()
),
(
  'plan_premium',
  'Premium Plan',
  15.00,
  'monthly',
  ARRAY[
    'Unlimited patients',
    'Advanced appointment scheduling',
    'Complete patient management',
    'WhatsApp integration',
    'Priority support',
    'Advanced analytics',
    'Inventory management',
    'Multi-location support',
    'Custom branding'
  ],
  true,
  NOW()
),
(
  'plan_trial',
  'Trial',
  0.00,
  'monthly',
  ARRAY[
    'All Premium features',
    '7-day free trial',
    'No credit card required'
  ],
  true,
  NOW()
);

-- Create payment_methods table for storing customer payment info
CREATE TABLE IF NOT EXISTS payment_methods (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    stripe_payment_method_id VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL DEFAULT 'card',
    card_brand VARCHAR(50),
    card_last4 VARCHAR(4),
    card_exp_month INTEGER,
    card_exp_year INTEGER,
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create subscription_transactions table for payment history
CREATE TABLE IF NOT EXISTS subscription_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    subscription_id UUID NOT NULL REFERENCES subscriptions(id) ON DELETE CASCADE,
    stripe_payment_intent_id VARCHAR(255),
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    status VARCHAR(50) NOT NULL,
    payment_method_id UUID REFERENCES payment_methods(id),
    transaction_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    billing_period_start TIMESTAMP WITH TIME ZONE,
    billing_period_end TIMESTAMP WITH TIME ZONE,
    invoice_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS for new tables
ALTER TABLE payment_methods ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscription_transactions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Doctors can manage their own payment methods" ON payment_methods
    FOR ALL TO authenticated
    USING (doctor_id = auth.uid());

CREATE POLICY "Doctors can view their own transactions" ON subscription_transactions
    FOR SELECT TO authenticated
    USING (subscription_id IN (
        SELECT id FROM subscriptions WHERE doctor_id = auth.uid()
    ));

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_payment_methods_doctor_id ON payment_methods(doctor_id);
CREATE INDEX IF NOT EXISTS idx_subscription_transactions_subscription_id ON subscription_transactions(subscription_id);
CREATE INDEX IF NOT EXISTS idx_subscription_transactions_status ON subscription_transactions(status);

SELECT 'Subscription and payment tables created successfully!' as status;
