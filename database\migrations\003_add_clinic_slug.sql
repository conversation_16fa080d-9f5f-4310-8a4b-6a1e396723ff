-- Add clinic_slug column to doctors table for unique booking URLs
-- This ensures each doctor gets a unique booking URL

-- Add clinic_slug column
ALTER TABLE doctors ADD COLUMN clinic_slug VARCHAR(255) UNIQUE;

-- Create index for fast slug lookups
CREATE INDEX idx_doctors_clinic_slug ON doctors(clinic_slug);

-- Function to generate unique clinic slug
CREATE OR REPLACE FUNCTION generate_unique_clinic_slug(clinic_name_input VARCHAR(255), doctor_id_input UUID DEFAULT NULL)
RETURNS VARCHAR(255) AS $$
DECLARE
    base_slug VARCHAR(255);
    final_slug VARCHAR(255);
    counter INTEGER := 0;
    slug_exists BOOLEAN;
BEGIN
    -- Generate base slug from clinic name
    base_slug := LOWER(TRIM(clinic_name_input));
    base_slug := REGEXP_REPLACE(base_slug, '[^a-z0-9\s]', '', 'g');
    base_slug := REGEXP_REPLACE(base_slug, '\s+', '-', 'g');
    base_slug := TRIM(base_slug, '-');
    
    -- Ensure minimum length
    IF LENGTH(base_slug) < 3 THEN
        base_slug := 'clinic-' || base_slug;
    END IF;
    
    -- Check if base slug is available
    final_slug := base_slug;
    
    LOOP
        -- Check if slug exists (excluding current doctor if updating)
        SELECT EXISTS(
            SELECT 1 FROM doctors 
            WHERE clinic_slug = final_slug 
            AND (doctor_id_input IS NULL OR id != doctor_id_input)
        ) INTO slug_exists;
        
        -- If slug doesn't exist, we can use it
        IF NOT slug_exists THEN
            EXIT;
        END IF;
        
        -- If slug exists, add counter
        counter := counter + 1;
        final_slug := base_slug || '-' || counter;
        
        -- Safety check to prevent infinite loop
        IF counter > 9999 THEN
            final_slug := base_slug || '-' || EXTRACT(EPOCH FROM NOW())::INTEGER;
            EXIT;
        END IF;
    END LOOP;
    
    RETURN final_slug;
END;
$$ LANGUAGE plpgsql;

-- Update existing doctors with unique slugs
UPDATE doctors 
SET clinic_slug = generate_unique_clinic_slug(clinic_name, id)
WHERE clinic_slug IS NULL;

-- Make clinic_slug NOT NULL after populating existing records
ALTER TABLE doctors ALTER COLUMN clinic_slug SET NOT NULL;

-- Add trigger to auto-generate slug for new doctors
CREATE OR REPLACE FUNCTION auto_generate_clinic_slug()
RETURNS TRIGGER AS $$
BEGIN
    -- Only generate slug if not provided
    IF NEW.clinic_slug IS NULL OR NEW.clinic_slug = '' THEN
        NEW.clinic_slug := generate_unique_clinic_slug(NEW.clinic_name, NEW.id);
    ELSE
        -- Validate provided slug is unique
        IF EXISTS(SELECT 1 FROM doctors WHERE clinic_slug = NEW.clinic_slug AND id != NEW.id) THEN
            RAISE EXCEPTION 'Clinic slug "%" already exists', NEW.clinic_slug;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for INSERT
CREATE TRIGGER trigger_auto_generate_clinic_slug_insert
    BEFORE INSERT ON doctors
    FOR EACH ROW
    EXECUTE FUNCTION auto_generate_clinic_slug();

-- Create trigger for UPDATE (when clinic_name changes)
CREATE TRIGGER trigger_auto_generate_clinic_slug_update
    BEFORE UPDATE ON doctors
    FOR EACH ROW
    WHEN (OLD.clinic_name IS DISTINCT FROM NEW.clinic_name AND (NEW.clinic_slug IS NULL OR NEW.clinic_slug = ''))
    EXECUTE FUNCTION auto_generate_clinic_slug();

-- Add comment
COMMENT ON COLUMN doctors.clinic_slug IS 'Unique URL slug for public booking page (e.g., "hassan-dental-clinic")';
