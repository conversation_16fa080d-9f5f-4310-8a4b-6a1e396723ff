-- =============================================
-- SIMPLE DATABASE FIX FOR DENTOPRO SAAS
-- =============================================
-- Run this in Supabase SQL Editor to fix registration issues
-- This is a simplified version that avoids RLS policy conflicts

-- =============================================
-- 1. ADD MISSING CLINIC_SLUG COLUMN
-- =============================================

-- Add clinic_slug column for unique booking URLs
ALTER TABLE doctors ADD COLUMN IF NOT EXISTS clinic_slug VARCHAR(255);

-- Create unique constraint on clinic_slug (drop first if exists)
DO $$ 
BEGIN
    IF EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'unique_clinic_slug'
    ) THEN
        ALTER TABLE doctors DROP CONSTRAINT unique_clinic_slug;
    END IF;
    ALTER TABLE doctors ADD CONSTRAINT unique_clinic_slug UNIQUE (clinic_slug);
END $$;

-- Create index for fast slug lookups
CREATE INDEX IF NOT EXISTS idx_doctors_clinic_slug ON doctors(clinic_slug);

-- =============================================
-- 2. FIX LICENSE_NUMBER CONSTRAINT
-- =============================================

-- Make license_number optional (not required for all doctors)
ALTER TABLE doctors ALTER COLUMN license_number DROP NOT NULL;

-- Update license_number constraint to allow NULL
DO $$ 
BEGIN
    IF EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'doctors_license_number_key'
    ) THEN
        ALTER TABLE doctors DROP CONSTRAINT doctors_license_number_key;
    END IF;
END $$;

-- Add new unique constraint that allows NULL values
CREATE UNIQUE INDEX IF NOT EXISTS unique_license_number_not_null 
ON doctors (license_number) 
WHERE license_number IS NOT NULL AND license_number != '';

-- =============================================
-- 3. UNIQUE SLUG GENERATION FUNCTION
-- =============================================

-- Function to generate unique clinic slug
CREATE OR REPLACE FUNCTION generate_unique_clinic_slug(clinic_name_input VARCHAR(255), doctor_id_input UUID DEFAULT NULL)
RETURNS VARCHAR(255) AS $$
DECLARE
    base_slug VARCHAR(255);
    final_slug VARCHAR(255);
    counter INTEGER := 0;
    slug_exists BOOLEAN;
BEGIN
    -- Generate base slug from clinic name
    base_slug := LOWER(TRIM(clinic_name_input));
    base_slug := REGEXP_REPLACE(base_slug, '[^a-z0-9\s]', '', 'g');
    base_slug := REGEXP_REPLACE(base_slug, '\s+', '-', 'g');
    base_slug := TRIM(base_slug, '-');
    
    -- Ensure minimum length
    IF LENGTH(base_slug) < 3 THEN
        base_slug := 'clinic-' || base_slug;
    END IF;
    
    -- Check if base slug is available
    final_slug := base_slug;
    
    LOOP
        -- Check if slug exists (excluding current doctor if updating)
        SELECT EXISTS(
            SELECT 1 FROM doctors 
            WHERE clinic_slug = final_slug 
            AND (doctor_id_input IS NULL OR id != doctor_id_input)
        ) INTO slug_exists;
        
        -- If slug doesn't exist, we can use it
        IF NOT slug_exists THEN
            EXIT;
        END IF;
        
        -- Increment counter and try again
        counter := counter + 1;
        final_slug := base_slug || '-' || counter;
        
        -- Safety check to prevent infinite loop
        IF counter > 1000 THEN
            final_slug := base_slug || '-' || EXTRACT(EPOCH FROM NOW())::INTEGER;
            EXIT;
        END IF;
    END LOOP;
    
    RETURN final_slug;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- 4. AUTO-GENERATE CLINIC SLUGS FOR EXISTING DOCTORS
-- =============================================

-- Update existing doctors without clinic_slug
UPDATE doctors 
SET clinic_slug = generate_unique_clinic_slug(clinic_name, id)
WHERE clinic_slug IS NULL OR clinic_slug = '';

-- =============================================
-- 5. TRIGGER TO AUTO-GENERATE SLUG ON INSERT/UPDATE
-- =============================================

-- Function to auto-generate clinic slug
CREATE OR REPLACE FUNCTION auto_generate_clinic_slug()
RETURNS TRIGGER AS $$
BEGIN
    -- Only generate if clinic_slug is empty or NULL
    IF NEW.clinic_slug IS NULL OR NEW.clinic_slug = '' THEN
        NEW.clinic_slug := generate_unique_clinic_slug(NEW.clinic_name, NEW.id);
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for INSERT
DROP TRIGGER IF EXISTS trigger_auto_generate_clinic_slug_insert ON doctors;
CREATE TRIGGER trigger_auto_generate_clinic_slug_insert
    BEFORE INSERT ON doctors
    FOR EACH ROW
    EXECUTE FUNCTION auto_generate_clinic_slug();

-- Create trigger for UPDATE
DROP TRIGGER IF EXISTS trigger_auto_generate_clinic_slug_update ON doctors;
CREATE TRIGGER trigger_auto_generate_clinic_slug_update
    BEFORE UPDATE ON doctors
    FOR EACH ROW
    WHEN (OLD.clinic_name IS DISTINCT FROM NEW.clinic_name OR NEW.clinic_slug IS NULL OR NEW.clinic_slug = '')
    EXECUTE FUNCTION auto_generate_clinic_slug();

-- =============================================
-- 6. ADD MISSING SUBSCRIPTION COLUMNS
-- =============================================

-- Ensure subscription columns exist with proper defaults
ALTER TABLE doctors ADD COLUMN IF NOT EXISTS subscription_plan VARCHAR(50) DEFAULT 'starter';
ALTER TABLE doctors ADD COLUMN IF NOT EXISTS subscription_status VARCHAR(50) DEFAULT 'trial';
ALTER TABLE doctors ADD COLUMN IF NOT EXISTS trial_end_date TIMESTAMP WITH TIME ZONE;

-- Update existing doctors to have proper trial dates
UPDATE doctors 
SET trial_end_date = created_at + INTERVAL '14 days'
WHERE trial_end_date IS NULL AND subscription_status = 'trial';

-- =============================================
-- 7. VALIDATION FUNCTIONS
-- =============================================

-- Function to check for duplicate emails
CREATE OR REPLACE FUNCTION check_duplicate_email(email_input VARCHAR(255), doctor_id_input UUID DEFAULT NULL)
RETURNS TABLE(
    exists_in_doctors BOOLEAN,
    existing_doctor_name VARCHAR(255),
    existing_clinic_name VARCHAR(255)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        EXISTS(
            SELECT 1 FROM doctors 
            WHERE email = email_input 
            AND (doctor_id_input IS NULL OR id != doctor_id_input)
        ) as exists_in_doctors,
        (SELECT full_name FROM doctors WHERE email = email_input LIMIT 1) as existing_doctor_name,
        (SELECT clinic_name FROM doctors WHERE email = email_input LIMIT 1) as existing_clinic_name;
END;
$$ LANGUAGE plpgsql;

-- Function to check for similar clinic names
CREATE OR REPLACE FUNCTION check_similar_clinic_names(clinic_name_input VARCHAR(255), doctor_id_input UUID DEFAULT NULL)
RETURNS TABLE(
    similar_clinic_name VARCHAR(255),
    similarity_score REAL,
    suggested_slug VARCHAR(255)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        d.clinic_name as similar_clinic_name,
        similarity(LOWER(d.clinic_name), LOWER(clinic_name_input)) as similarity_score,
        generate_unique_clinic_slug(clinic_name_input, doctor_id_input) as suggested_slug
    FROM doctors d
    WHERE (doctor_id_input IS NULL OR d.id != doctor_id_input)
    AND similarity(LOWER(d.clinic_name), LOWER(clinic_name_input)) > 0.6
    ORDER BY similarity_score DESC
    LIMIT 5;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- 8. CREATE INDEXES FOR PERFORMANCE
-- =============================================

-- Indexes for common queries
CREATE INDEX IF NOT EXISTS idx_doctors_email ON doctors(email);
CREATE INDEX IF NOT EXISTS idx_doctors_subscription_status ON doctors(subscription_status);
CREATE INDEX IF NOT EXISTS idx_doctors_is_active ON doctors(is_active);
CREATE INDEX IF NOT EXISTS idx_doctors_approved_at ON doctors(approved_at);

-- =============================================
-- VERIFICATION
-- =============================================

-- Test the functions
DO $$
BEGIN
    RAISE NOTICE '=== TESTING FUNCTIONS ===';
    
    -- Test slug generation
    RAISE NOTICE 'Test slug generation: %', generate_unique_clinic_slug('Test Clinic Name');
    
    RAISE NOTICE '=== VERIFICATION COMPLETE ===';
    RAISE NOTICE '✅ Database fix applied successfully!';
    RAISE NOTICE '✅ You can now test doctor registration';
END $$;
