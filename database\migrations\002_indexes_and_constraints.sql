-- DentoPro SaaS Platform - Indexes and Constraints
-- Migration: 002_indexes_and_constraints.sql
-- Description: Create performance indexes and additional constraints

-- =============================================
-- PERFORMANCE INDEXES
-- =============================================

-- Doctor-based indexes for data isolation (most important)
CREATE INDEX idx_patients_doctor_id ON patients(doctor_id);
CREATE INDEX idx_appointments_doctor_id ON appointments(doctor_id);
CREATE INDEX idx_treatments_doctor_id ON treatments(doctor_id);
CREATE INDEX idx_materials_doctor_id ON materials(doctor_id);
CREATE INDEX idx_material_logs_doctor_id ON material_logs(doctor_id);
CREATE INDEX idx_prescriptions_doctor_id ON prescriptions(doctor_id);
CREATE INDEX idx_invoices_doctor_id ON invoices(doctor_id);
CREATE INDEX idx_notifications_doctor_id ON notifications(doctor_id);
CREATE INDEX idx_file_uploads_doctor_id ON file_uploads(doctor_id);
CREATE INDEX idx_clinic_settings_doctor_id ON clinic_settings(doctor_id);

-- Authentication and lookup indexes
CREATE INDEX idx_doctors_email ON doctors(email);
CREATE INDEX idx_platform_admins_email ON platform_admins(email);
CREATE INDEX idx_patients_phone ON patients(phone);
CREATE INDEX idx_patients_email ON patients(email);

-- Appointment scheduling indexes
CREATE INDEX idx_appointments_date_time ON appointments(appointment_date, appointment_time);
CREATE INDEX idx_appointments_doctor_date ON appointments(doctor_id, appointment_date);
CREATE INDEX idx_appointments_patient_date ON appointments(patient_id, appointment_date);
CREATE INDEX idx_appointments_status ON appointments(doctor_id, status);

-- Treatment and medical records indexes
CREATE INDEX idx_treatments_patient_date ON treatments(patient_id, created_at);
CREATE INDEX idx_treatments_appointment ON treatments(appointment_id);
CREATE INDEX idx_treatments_tooth ON treatments(doctor_id, tooth_number);

-- Inventory management indexes
CREATE INDEX idx_materials_low_stock ON materials(doctor_id, current_stock, low_threshold);
CREATE INDEX idx_materials_category ON materials(doctor_id, category);
CREATE INDEX idx_materials_expiry ON materials(doctor_id, expiry_date) WHERE expiry_date IS NOT NULL;
CREATE INDEX idx_material_logs_material_date ON material_logs(material_id, created_at);
CREATE INDEX idx_material_logs_type ON material_logs(doctor_id, type);

-- Prescription indexes
CREATE INDEX idx_prescriptions_valid ON prescriptions(valid_until, is_dispensed);
CREATE INDEX idx_prescriptions_patient ON prescriptions(patient_id, issued_at);
CREATE INDEX idx_prescriptions_number ON prescriptions(prescription_number);

-- Billing and invoice indexes
CREATE INDEX idx_invoices_status ON invoices(doctor_id, status);
CREATE INDEX idx_invoices_due_date ON invoices(due_date) WHERE status != 'paid';
CREATE INDEX idx_invoices_patient ON invoices(patient_id, created_at);
CREATE INDEX idx_invoices_number ON invoices(invoice_number);

-- Subscription and billing indexes
CREATE INDEX idx_subscriptions_doctor ON subscriptions(doctor_id);
CREATE INDEX idx_subscriptions_status ON subscriptions(status, current_period_end);
CREATE INDEX idx_subscriptions_stripe ON subscriptions(stripe_subscription_id);

-- Notification indexes
CREATE INDEX idx_notifications_recipient ON notifications(recipient_type, recipient_id, status);
CREATE INDEX idx_notifications_scheduled ON notifications(scheduled_for) WHERE scheduled_for IS NOT NULL;
CREATE INDEX idx_notifications_type ON notifications(doctor_id, type);

-- File management indexes
CREATE INDEX idx_file_uploads_patient ON file_uploads(patient_id, created_at);
CREATE INDEX idx_file_uploads_category ON file_uploads(doctor_id, category);
CREATE INDEX idx_file_uploads_appointment ON file_uploads(appointment_id);

-- Analytics and monitoring indexes
CREATE INDEX idx_platform_analytics_metric ON platform_analytics(metric_type, recorded_at);
CREATE INDEX idx_platform_analytics_doctor ON platform_analytics(doctor_id, recorded_at);
CREATE INDEX idx_license_tracking_doctor ON license_tracking(doctor_id, is_active);
CREATE INDEX idx_license_tracking_domain ON license_tracking(domain, last_active);
CREATE INDEX idx_system_logs_created_at ON system_logs(created_at);
CREATE INDEX idx_system_logs_user ON system_logs(user_id, user_type);
CREATE INDEX idx_system_logs_doctor ON system_logs(doctor_id, created_at);

-- =============================================
-- ADDITIONAL CONSTRAINTS
-- =============================================

-- Check constraints for data validation
ALTER TABLE doctors ADD CONSTRAINT chk_subscription_plan 
    CHECK (subscription_plan IN ('trial', 'basic', 'premium'));

ALTER TABLE doctors ADD CONSTRAINT chk_subscription_status 
    CHECK (subscription_status IN ('trial', 'active', 'suspended', 'cancelled'));

ALTER TABLE subscriptions ADD CONSTRAINT chk_subscription_amount 
    CHECK (amount >= 0);

ALTER TABLE subscriptions ADD CONSTRAINT chk_subscription_currency 
    CHECK (currency IN ('USD', 'EUR', 'EGP'));

ALTER TABLE patients ADD CONSTRAINT chk_patient_gender 
    CHECK (gender IN ('male', 'female', 'other'));

ALTER TABLE appointments ADD CONSTRAINT chk_appointment_duration 
    CHECK (duration > 0 AND duration <= 480); -- Max 8 hours

ALTER TABLE appointments ADD CONSTRAINT chk_appointment_status 
    CHECK (status IN ('scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'));

ALTER TABLE appointments ADD CONSTRAINT chk_appointment_type 
    CHECK (appointment_type IN ('consultation', 'cleaning', 'filling', 'root_canal', 'extraction', 'checkup', 'emergency', 'follow_up'));

ALTER TABLE treatments ADD CONSTRAINT chk_treatment_cost 
    CHECK (cost >= 0);

ALTER TABLE materials ADD CONSTRAINT chk_material_stock 
    CHECK (current_stock >= 0);

ALTER TABLE materials ADD CONSTRAINT chk_material_threshold 
    CHECK (low_threshold >= 0);

ALTER TABLE materials ADD CONSTRAINT chk_material_cost 
    CHECK (cost_per_unit >= 0);

ALTER TABLE material_logs ADD CONSTRAINT chk_material_log_type 
    CHECK (type IN ('stock_in', 'stock_out', 'adjustment', 'expired', 'damaged'));

ALTER TABLE invoices ADD CONSTRAINT chk_invoice_amounts 
    CHECK (subtotal >= 0 AND tax_amount >= 0 AND discount_amount >= 0 AND total_amount >= 0);

ALTER TABLE invoices ADD CONSTRAINT chk_invoice_status 
    CHECK (status IN ('draft', 'sent', 'paid', 'overdue', 'cancelled'));

ALTER TABLE notifications ADD CONSTRAINT chk_notification_recipient_type 
    CHECK (recipient_type IN ('super_admin', 'doctor', 'patient'));

ALTER TABLE notifications ADD CONSTRAINT chk_notification_status 
    CHECK (status IN ('pending', 'sent', 'delivered', 'read', 'failed'));

ALTER TABLE notifications ADD CONSTRAINT chk_notification_priority 
    CHECK (priority IN ('low', 'normal', 'high', 'urgent'));

ALTER TABLE system_logs ADD CONSTRAINT chk_log_level 
    CHECK (level IN ('info', 'warn', 'error', 'debug'));

ALTER TABLE system_logs ADD CONSTRAINT chk_user_type 
    CHECK (user_type IN ('super_admin', 'doctor', 'patient', 'system'));

-- =============================================
-- TRIGGERS FOR UPDATED_AT
-- =============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to relevant tables
CREATE TRIGGER update_platform_admins_updated_at BEFORE UPDATE ON platform_admins 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_doctors_updated_at BEFORE UPDATE ON doctors 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscriptions_updated_at BEFORE UPDATE ON subscriptions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_clinic_settings_updated_at BEFORE UPDATE ON clinic_settings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_patients_updated_at BEFORE UPDATE ON patients 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_appointments_updated_at BEFORE UPDATE ON appointments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_treatments_updated_at BEFORE UPDATE ON treatments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_materials_updated_at BEFORE UPDATE ON materials 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_invoices_updated_at BEFORE UPDATE ON invoices 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- FUNCTIONS FOR BUSINESS LOGIC
-- =============================================

-- Function to generate unique prescription numbers
CREATE OR REPLACE FUNCTION generate_prescription_number(doctor_id UUID)
RETURNS VARCHAR(100) AS $$
DECLARE
    clinic_prefix VARCHAR(10);
    sequence_num INTEGER;
    prescription_num VARCHAR(100);
BEGIN
    -- Get clinic prefix from doctor's license number (first 3 chars)
    SELECT UPPER(LEFT(license_number, 3)) INTO clinic_prefix 
    FROM doctors WHERE id = doctor_id;
    
    -- Get next sequence number for this doctor
    SELECT COALESCE(MAX(CAST(SUBSTRING(prescription_number FROM '[0-9]+$') AS INTEGER)), 0) + 1
    INTO sequence_num
    FROM prescriptions 
    WHERE prescriptions.doctor_id = generate_prescription_number.doctor_id;
    
    -- Format: CLI001-YYYYMMDD-0001
    prescription_num := clinic_prefix || '-' || TO_CHAR(CURRENT_DATE, 'YYYYMMDD') || '-' || LPAD(sequence_num::TEXT, 4, '0');
    
    RETURN prescription_num;
END;
$$ LANGUAGE plpgsql;

-- Function to generate unique invoice numbers
CREATE OR REPLACE FUNCTION generate_invoice_number(doctor_id UUID)
RETURNS VARCHAR(100) AS $$
DECLARE
    clinic_prefix VARCHAR(10);
    sequence_num INTEGER;
    invoice_num VARCHAR(100);
BEGIN
    -- Get clinic prefix from doctor's license number (first 3 chars)
    SELECT UPPER(LEFT(license_number, 3)) INTO clinic_prefix 
    FROM doctors WHERE id = doctor_id;
    
    -- Get next sequence number for this doctor
    SELECT COALESCE(MAX(CAST(SUBSTRING(invoice_number FROM '[0-9]+$') AS INTEGER)), 0) + 1
    INTO sequence_num
    FROM invoices 
    WHERE invoices.doctor_id = generate_invoice_number.doctor_id;
    
    -- Format: CLI-INV-YYYYMMDD-0001
    invoice_num := clinic_prefix || '-INV-' || TO_CHAR(CURRENT_DATE, 'YYYYMMDD') || '-' || LPAD(sequence_num::TEXT, 4, '0');
    
    RETURN invoice_num;
END;
$$ LANGUAGE plpgsql;

-- Function to check material stock levels
CREATE OR REPLACE FUNCTION check_low_stock(doctor_id UUID)
RETURNS TABLE(material_id UUID, material_name VARCHAR, current_stock DECIMAL, threshold DECIMAL) AS $$
BEGIN
    RETURN QUERY
    SELECT m.id, m.name, m.current_stock, m.low_threshold
    FROM materials m
    WHERE m.doctor_id = check_low_stock.doctor_id 
    AND m.is_active = true
    AND m.current_stock <= m.low_threshold;
END;
$$ LANGUAGE plpgsql;
