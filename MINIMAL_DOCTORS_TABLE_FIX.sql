-- MINIMAL FIX FOR DOCTORS TABLE
-- Run this in Supabase SQL Editor to add only the missing clinic_slug column

-- 1. Add clinic_slug column if it doesn't exist
ALTER TABLE doctors ADD COLUMN IF NOT EXISTS clinic_slug VARCHAR(255);

-- 2. Make license_number nullable (your current schema requires it)
ALTER TABLE doctors ALTER COLUMN license_number DROP NOT NULL;

-- 3. Create a simple unique constraint for clinic_slug
DO $$ 
BEGIN
    -- Drop existing constraint if it exists
    IF EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'unique_clinic_slug'
    ) THEN
        ALTER TABLE doctors DROP CONSTRAINT unique_clinic_slug;
    END IF;
    
    -- Add new constraint
    ALTER TABLE doctors ADD CONSTRAINT unique_clinic_slug UNIQUE (clinic_slug);
EXCEPTION
    WHEN duplicate_object THEN
        NULL; -- Ignore if constraint already exists
END $$;

-- 4. Update existing doctors to have clinic_slug
UPDATE doctors 
SET clinic_slug = LOWER(REGEXP_REPLACE(clinic_name, '[^a-zA-Z0-9]', '-', 'g')) || '-' || id::text
WHERE clinic_slug IS NULL OR clinic_slug = '';

-- 5. Check the results
SELECT 
    id, 
    email, 
    full_name, 
    clinic_name, 
    clinic_slug,
    license_number,
    subscription_plan,
    subscription_status
FROM doctors 
LIMIT 5;

-- 6. Show table structure
SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'doctors'
ORDER BY ordinal_position;

SELECT 'Minimal doctors table fix completed!' as result;
