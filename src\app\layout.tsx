import type { Metada<PERSON> } from "next";
import "./globals.css";
import { AuthProvider } from "@/hooks/useAuth";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { APP_NAME } from "@/lib/constants";

export const metadata: Metadata = {
  title: `${APP_NAME} - Complete Dental Clinic Management`,
  description: "Streamline your dental practice with our comprehensive SaaS platform. Manage patients, appointments, treatments, inventory, and more - all in one place.",
  keywords: "dental clinic management, dental software, patient management, appointment scheduling, dental SaaS",
  authors: [{ name: "DentoPro Team" }],
  viewport: "width=device-width, initial-scale=1",
  robots: "index, follow",
  openGraph: {
    title: `${APP_NAME} - Complete Dental Clinic Management`,
    description: "Streamline your dental practice with our comprehensive SaaS platform.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: `${APP_NAME} - Complete Dental Clinic Management`,
    description: "Streamline your dental practice with our comprehensive SaaS platform.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="antialiased">
        <ThemeProvider
          defaultTheme="system"
          storageKey="dentopro-ui-theme"
        >
          <AuthProvider>
            {children}
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
