'use client';

import { useState, useEffect, useContext, createContext, ReactNode } from 'react';
import { User as SupabaseUser, Session } from '@supabase/supabase-js';
import { useRouter } from 'next/navigation';
import { User, UserRole } from '@/types';
import { SupabaseUtils, createClientSupabaseClient } from '@/lib/supabase';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<any>;
  loginWithGoogle: () => Promise<any>;
  loginWithGitHub: () => Promise<any>;
  loginWithMicrosoft: () => Promise<any>;
  logout: () => Promise<void>;
  register: (userData: any) => Promise<any>;
  resetPassword: (email: string) => Promise<any>;
  enable2FA: (phoneNumber: string) => Promise<any>;
  verify2FA: (code: string) => Promise<any>;
  disable2FA: () => Promise<any>;
  is2FAEnabled: boolean;
  isAuthenticated: boolean;
  hasRole: (role: UserRole) => boolean;
  isSuperAdmin: boolean;
  isDoctor: boolean;
  isPatient: boolean;
  doctorId: string | null;
  patientId: string | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [doctorId, setDoctorId] = useState<string | null>(null);
  const [patientId, setPatientId] = useState<string | null>(null);
  const [is2FAEnabled, setIs2FAEnabled] = useState(false);

  const router = useRouter();
  const supabase = createClientSupabaseClient();

  useEffect(() => {
    // Get initial session for THIS browser only (isolated)
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        if (error) {
          // Handle common auth errors gracefully
          if (error.message.includes('refresh_token_not_found') ||
              error.message.includes('Invalid Refresh Token') ||
              error.message === 'Auth session missing!') {
            console.log('No valid session found, continuing without auth');
            setSession(null);
            setUser(null);
          } else {
            console.error('Error getting session:', error);
          }
        } else {
          await handleAuthStateChange(session);
        }
      } catch (error) {
        console.error('Session initialization error:', error);
        // Don't fail - just continue with no session
        setSession(null);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    getInitialSession();

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email);
        try {
          await handleAuthStateChange(session);
        } catch (error) {
          console.error('Error handling auth state change:', error);
          // Continue gracefully
        }
        setLoading(false);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [supabase.auth]);

  const handleAuthStateChange = async (session: Session | null) => {
    setSession(session);

    if (session?.user) {
      // Convert Supabase user to our User type
      const userRole = await getUserRole(session.user);
      const userWithRole: User = {
        id: session.user.id,
        email: session.user.email || '',
        role: userRole,
        createdAt: new Date(session.user.created_at),
        updatedAt: new Date(session.user.updated_at || session.user.created_at)
      };

      setUser(userWithRole);

      // Set role-specific IDs
      if (userRole === UserRole.DOCTOR) {
        setDoctorId(session.user.id);
        setPatientId(null);
      } else if (userRole === UserRole.PATIENT) {
        setPatientId(session.user.id);
        const doctorId = await SupabaseUtils.getCurrentDoctorId();
        setDoctorId(doctorId);
      } else {
        setDoctorId(null);
        setPatientId(null);
      }
    } else {
      setUser(null);
      setDoctorId(null);
      setPatientId(null);
    }
  };

  const getUserRole = async (supabaseUser: SupabaseUser): Promise<UserRole> => {
    // Check user metadata first
    const metadataRole = supabaseUser.user_metadata?.role || supabaseUser.app_metadata?.role;
    if (metadataRole) {
      return metadataRole as UserRole;
    }

    // Check if user exists in platform_admins table
    try {
      const { data: admin } = await supabase
        .from('platform_admins')
        .select('id')
        .eq('id', supabaseUser.id)
        .single();

      if (admin) {
        return UserRole.SUPER_ADMIN;
      }
    } catch (error) {
      // User not found in platform_admins, continue checking
    }

    // Check if user exists in doctors table
    try {
      const { data: doctor } = await supabase
        .from('doctors')
        .select('id')
        .eq('id', supabaseUser.id)
        .single();

      if (doctor) {
        return UserRole.DOCTOR;
      }
    } catch (error) {
      // User not found in doctors, continue checking
    }

    // Check if user exists in patients table
    try {
      const { data: patient } = await supabase
        .from('patients')
        .select('id')
        .eq('id', supabaseUser.id)
        .single();

      if (patient) {
        return UserRole.PATIENT;
      }
    } catch (error) {
      // User not found in patients
    }

    // Default to patient role for new users
    return UserRole.PATIENT;
  };

  const login = async (email: string, password: string) => {
    setLoading(true);
    try {
      const result = await SupabaseUtils.signInWithPassword(email, password);
      if (result.error) {
        throw result.error;
      }
      return result;
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const loginWithGoogle = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      });

      if (error) {
        throw error;
      }
      return { data, error: null };
    } catch (error) {
      console.error('Google login failed:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const loginWithGitHub = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'github',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
        },
      });

      if (error) {
        throw error;
      }
      return { data, error: null };
    } catch (error) {
      console.error('GitHub login failed:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const loginWithMicrosoft = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'azure',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
          scopes: 'email profile',
        },
      });

      if (error) {
        throw error;
      }
      return { data, error: null };
    } catch (error) {
      console.error('Microsoft login failed:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    setLoading(true);
    try {
      const { error } = await SupabaseUtils.signOut();
      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Logout failed:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const register = async (userData: {
    email: string;
    password: string;
    fullName?: string;
    clinicName?: string;
    userType?: string;
    subscriptionPlan?: string;
    licenseNumber?: string;
    role?: UserRole;
    metadata?: any;
  }) => {
    setLoading(true);
    const supabase = createClientSupabaseClient();

    console.log('🔍 Registration called with userData:', userData);

    try {
      // Determine role from userType or use provided role
      const userRole = userData.role || (userData.userType === 'doctor' ? UserRole.DOCTOR : UserRole.PATIENT);

      // Prepare metadata
      const metadata = {
        role: userRole,
        full_name: userData.fullName,
        clinic_name: userData.clinicName,
        user_type: userData.userType,
        ...userData.metadata
      };

      const result = await SupabaseUtils.signUp(
        userData.email,
        userData.password,
        metadata
      );

      if (result.error) {
        throw result.error;
      }

      // If user is a doctor, create doctor record in database
      if (userData.userType === 'doctor' && result.data.user) {
        try {
          console.log('🔍 Creating doctor record for user:', result.data.user.id);
          console.log('🔍 User data received:', userData);

          // Generate simple clinic slug
          const baseSlug = userData.clinicName
            ? userData.clinicName.toLowerCase().replace(/[^a-z0-9]/g, '-').replace(/-+/g, '-').trim('-')
            : 'clinic';
          const clinicSlug = baseSlug + '-' + Date.now();

          // Create doctor data matching your exact table structure from setup-complete-final.sql
          const doctorData = {
            id: result.data.user.id,
            email: userData.email,
            password_hash: 'supabase_managed', // Required by your schema
            full_name: userData.fullName || '',
            license_number: userData.licenseNumber || 'TEMP-' + Date.now(), // Your schema requires this
            clinic_name: userData.clinicName || '',
            clinic_slug: clinicSlug, // Added by our fix
            subscription_plan: userData.subscriptionPlan || 'trial',
            subscription_status: 'trial',
            trial_end_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
            is_active: false
          };

          console.log('🔍 Doctor data to insert:', doctorData);

          // Create doctor record
          const { data: doctorRecord, error: doctorError } = await supabase
            .from('doctors')
            .insert(doctorData)
            .select()
            .single();

          if (doctorError) {
            console.error('❌ Failed to create doctor record:', doctorError);
            console.error('❌ Doctor data that failed:', doctorData);
            // Don't throw error here, user auth was successful
          } else {
            console.log('✅ Doctor record created successfully:', doctorRecord);
          }
        } catch (error) {
          console.error('❌ Error creating doctor record:', error);
        }
      }

      // Check if email confirmation is required
      if (result.data.user && !result.data.user.email_confirmed_at) {
        // Email confirmation required - redirect to check email page
        router.push('/auth/check-email?email=' + encodeURIComponent(userData.email));
        return result;
      }

      return result;
    } catch (error) {
      console.error('Registration failed:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (email: string) => {
    try {
      const result = await SupabaseUtils.resetPassword(email);
      if (result.error) {
        throw result.error;
      }
      return result;
    } catch (error) {
      console.error('Password reset failed:', error);
      throw error;
    }
  };

  // Two-Factor Authentication Functions
  const enable2FA = async (phoneNumber: string) => {
    try {
      if (!user) {
        throw new Error('User must be logged in to enable 2FA');
      }

      // Generate and send verification code
      const response = await fetch('/api/auth/2fa/enable', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
          phoneNumber: phoneNumber,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to enable 2FA');
      }

      return result;
    } catch (error) {
      console.error('2FA enable failed:', error);
      throw error;
    }
  };

  const verify2FA = async (code: string) => {
    try {
      if (!user) {
        throw new Error('User must be logged in to verify 2FA');
      }

      const response = await fetch('/api/auth/2fa/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
          code: code,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to verify 2FA code');
      }

      // Update 2FA status
      setIs2FAEnabled(result.enabled);

      return result;
    } catch (error) {
      console.error('2FA verification failed:', error);
      throw error;
    }
  };

  const disable2FA = async () => {
    try {
      if (!user) {
        throw new Error('User must be logged in to disable 2FA');
      }

      const response = await fetch('/api/auth/2fa/disable', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to disable 2FA');
      }

      // Update 2FA status
      setIs2FAEnabled(false);

      return result;
    } catch (error) {
      console.error('2FA disable failed:', error);
      throw error;
    }
  };

  const hasRole = (role: UserRole): boolean => {
    return user?.role === role;
  };

  const value: AuthContextType = {
    user,
    session,
    loading,
    login,
    loginWithGoogle,
    loginWithGitHub,
    loginWithMicrosoft,
    logout,
    register,
    resetPassword,
    enable2FA,
    verify2FA,
    disable2FA,
    is2FAEnabled,
    isAuthenticated: !!user,
    hasRole,
    isSuperAdmin: hasRole(UserRole.SUPER_ADMIN),
    isDoctor: hasRole(UserRole.DOCTOR),
    isPatient: hasRole(UserRole.PATIENT),
    doctorId,
    patientId,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
