'use client';

import { useState, useEffect } from 'react';
import { createClientSupabaseClient } from '@/lib/supabase';

interface RealTimeIndicatorProps {
  table: string;
  className?: string;
}

export default function RealTimeIndicator({ table, className = '' }: RealTimeIndicatorProps) {
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [updateCount, setUpdateCount] = useState(0);

  useEffect(() => {
    const supabase = createClientSupabaseClient();
    
    const channel = supabase
      .channel(`realtime-indicator-${table}`)
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: table }, 
        (payload) => {
          console.log(`🔄 Real-time update detected in ${table}:`, payload);
          setLastUpdate(new Date());
          setUpdateCount(prev => prev + 1);
        }
      )
      .subscribe((status) => {
        console.log(`Real-time status for ${table}:`, status);
        setIsConnected(status === 'SUBSCRIBED');
      });

    return () => {
      supabase.removeChannel(channel);
    };
  }, [table]);

  return (
    <div className={`flex items-center space-x-2 text-xs ${className}`}>
      <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`}></div>
      <span className={isConnected ? 'text-green-600' : 'text-red-600'}>
        {isConnected ? 'Real-time Connected' : 'Disconnected'}
      </span>
      {lastUpdate && (
        <span className="text-gray-500">
          Last update: {lastUpdate.toLocaleTimeString()} ({updateCount} updates)
        </span>
      )}
    </div>
  );
}
