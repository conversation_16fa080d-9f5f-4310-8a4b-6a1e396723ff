-- Check current database schema and compare with required updates
-- Run this in your Supabase SQL Editor

-- 1. Check if clinic_slug column exists in doctors table
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'doctors' 
ORDER BY ordinal_position;

-- 2. Check for unique constraints on doctors table
SELECT 
    tc.constraint_name, 
    tc.constraint_type,
    kcu.column_name
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu 
    ON tc.constraint_name = kcu.constraint_name
WHERE tc.table_name = 'doctors' 
    AND tc.constraint_type IN ('UNIQUE', 'PRIMARY KEY');

-- 3. Check if clinic_slug generation functions exist
SELECT routine_name, routine_type 
FROM information_schema.routines 
WHERE routine_name LIKE '%clinic_slug%';

-- 4. Check if triggers exist for clinic_slug
SELECT trigger_name, event_manipulation, action_timing
FROM information_schema.triggers 
WHERE trigger_name LIKE '%clinic_slug%';

-- 5. Check current doctors data (if any)
SELECT id, email, full_name, clinic_name, 
       CASE WHEN EXISTS (
           SELECT 1 FROM information_schema.columns 
           WHERE table_name = 'doctors' AND column_name = 'clinic_slug'
       ) THEN clinic_slug ELSE 'COLUMN_NOT_EXISTS' END as clinic_slug
FROM doctors 
LIMIT 5;

-- 6. Check clinic_settings table structure
SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'clinic_settings' 
ORDER BY ordinal_position;
