import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          },
        },
      }
    );

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get doctor profile from database
    const { data: doctor, error: doctor<PERSON>rror } = await supabase
      .from('doctors')
      .select(`
        id,
        email,
        full_name,
        license_number,
        clinic_name,
        clinic_slug,
        phone,
        whatsapp,
        subscription_plan,
        subscription_status,
        trial_end_date,
        is_active,
        created_at
      `)
      .eq('id', user.id)
      .single();

    if (doctorError || !doctor) {
      return NextResponse.json(
        { error: 'Doctor profile not found' },
        { status: 404 }
      );
    }

    // Format the response
    const doctorProfile = {
      id: doctor.id,
      email: doctor.email,
      fullName: doctor.full_name,
      licenseNumber: doctor.license_number,
      clinicName: doctor.clinic_name,
      phone: doctor.phone,
      whatsapp: doctor.whatsapp,
      subscriptionPlan: doctor.subscription_plan,
      subscriptionStatus: doctor.subscription_status,
      trialEndDate: doctor.trial_end_date,
      isActive: doctor.is_active,
      joinedDate: doctor.created_at
    };

    return NextResponse.json(doctorProfile);

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          },
        },
      }
    );

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Update doctor profile
    const { data: updatedDoctor, error: updateError } = await supabase
      .from('doctors')
      .update({
        full_name: body.fullName,
        clinic_name: body.clinicName,
        phone: body.phone,
        whatsapp: body.whatsapp,
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id)
      .select()
      .single();

    if (updateError) {
      console.error('Update error:', updateError);
      return NextResponse.json(
        { error: 'Failed to update profile' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      doctor: updatedDoctor
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
