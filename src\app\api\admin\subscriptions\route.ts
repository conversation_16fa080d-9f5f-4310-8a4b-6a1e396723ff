import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          },
        },
      }
    );

    // Get current user and verify admin role
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Verify user is super admin - use service role to bypass RLS
    const adminSupabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        cookies: {
          getAll() { return []; },
          setAll() {},
        },
      }
    );

    const { data: admin, error: adminError } = await adminSupabase
      .from('platform_admins')
      .select('id')
      .eq('id', user.id)
      .single();

    if (adminError || !admin) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Get all doctors with subscription information
    const { data: doctors, error: doctorsError } = await supabase
      .from('doctors')
      .select(`
        id,
        email,
        full_name,
        clinic_name,
        subscription_plan,
        subscription_status,
        trial_end_date,
        created_at,
        updated_at
      `)
      .order('created_at', { ascending: false });

    if (doctorsError) {
      console.error('Error fetching doctors:', doctorsError);
      return NextResponse.json(
        { error: 'Failed to fetch subscription data' },
        { status: 500 }
      );
    }

    // Transform doctor data into subscription format
    const subscriptions = doctors?.map(doctor => {
      const isTrialActive = doctor.trial_end_date && new Date(doctor.trial_end_date) > new Date();
      const planPrice = getPlanPrice(doctor.subscription_plan);
      
      return {
        id: `sub_${doctor.id}`,
        doctorName: doctor.full_name,
        doctorEmail: doctor.email,
        planName: formatPlanName(doctor.subscription_plan),
        status: doctor.subscription_status,
        amount: planPrice,
        currency: 'USD',
        billingCycle: 'monthly' as const,
        currentPeriodStart: doctor.created_at,
        currentPeriodEnd: getNextBillingDate(doctor.created_at),
        trialEnd: doctor.trial_end_date,
        nextBillingDate: doctor.subscription_status === 'active' ? getNextBillingDate(doctor.created_at) : undefined
      };
    }) || [];

    console.log(`📊 Fetched ${subscriptions.length} subscriptions from Supabase`);

    return NextResponse.json(subscriptions);

  } catch (error) {
    console.error('Subscriptions API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper functions
function getPlanPrice(plan: string): number {
  const prices = {
    'trial': 0,
    'basic': 9.99,
    'premium': 15.99,
    'enterprise': 29.99
  };
  return prices[plan as keyof typeof prices] || 0;
}

function formatPlanName(plan: string): string {
  const names = {
    'trial': 'Trial',
    'basic': 'Basic Plan',
    'premium': 'Premium Plan', 
    'enterprise': 'Enterprise Plan'
  };
  return names[plan as keyof typeof names] || plan;
}

function getNextBillingDate(startDate: string): string {
  const date = new Date(startDate);
  date.setMonth(date.getMonth() + 1);
  return date.toISOString().split('T')[0];
}
