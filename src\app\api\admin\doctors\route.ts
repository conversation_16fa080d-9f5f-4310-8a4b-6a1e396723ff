import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          },
        },
      }
    );

    // Get current user and verify admin role
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Verify user is super admin - use service role to bypass RLS
    const adminSupabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        cookies: {
          getAll() { return []; },
          setAll() {},
        },
      }
    );

    const { data: admin, error: adminError } = await adminSupabase
      .from('platform_admins')
      .select('id')
      .eq('id', user.id)
      .single();

    if (adminError || !admin) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Get all doctors with their stats
    const { data: doctors, error: doctorsError } = await supabase
      .from('doctors')
      .select(`
        id,
        email,
        full_name,
        license_number,
        clinic_name,
        phone,
        whatsapp,
        subscription_plan,
        subscription_status,
        trial_end_date,
        last_login,
        is_active,
        created_at,
        updated_at
      `)
      .order('created_at', { ascending: false });

    if (doctorsError) {
      console.error('Error fetching doctors:', doctorsError);
      return NextResponse.json(
        { error: 'Failed to fetch doctors' },
        { status: 500 }
      );
    }

    // Get patient counts for each doctor
    const doctorIds = doctors?.map(d => d.id) || [];
    const { data: patientCounts } = await supabase
      .from('patients')
      .select('doctor_id')
      .in('doctor_id', doctorIds);

    // Get appointment counts for each doctor
    const { data: appointmentCounts } = await supabase
      .from('appointments')
      .select('doctor_id')
      .in('doctor_id', doctorIds);

    // Create lookup maps for counts
    const patientCountMap = new Map();
    const appointmentCountMap = new Map();

    patientCounts?.forEach(p => {
      patientCountMap.set(p.doctor_id, (patientCountMap.get(p.doctor_id) || 0) + 1);
    });

    appointmentCounts?.forEach(a => {
      appointmentCountMap.set(a.doctor_id, (appointmentCountMap.get(a.doctor_id) || 0) + 1);
    });

    // Format the response
    const formattedDoctors = doctors?.map(doctor => ({
      id: doctor.id,
      fullName: doctor.full_name,
      email: doctor.email,
      licenseNumber: doctor.license_number,
      clinicName: doctor.clinic_name,
      phoneNumber: doctor.phone,
      whatsappNumber: doctor.whatsapp,
      subscriptionPlan: doctor.subscription_plan,
      subscriptionStatus: doctor.subscription_status,
      trialEndDate: doctor.trial_end_date,
      joinedDate: doctor.created_at,
      lastActive: doctor.last_login || doctor.updated_at,
      isActive: doctor.is_active,
      totalPatients: patientCountMap.get(doctor.id) || 0,
      totalAppointments: appointmentCountMap.get(doctor.id) || 0
    })) || [];

    return NextResponse.json(formattedDoctors);

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { doctorId, action, ...updateData } = body;

    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          },
        },
      }
    );

    // Get current user and verify admin role
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Verify user is super admin
    const { data: admin, error: adminError } = await supabase
      .from('platform_admins')
      .select('id')
      .eq('id', user.id)
      .single();

    if (adminError || !admin) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    let updateFields: any = { updated_at: new Date().toISOString() };

    switch (action) {
      case 'activate':
        updateFields.is_active = true;
        break;
      case 'deactivate':
        updateFields.is_active = false;
        break;
      case 'approve':
        updateFields.approved_by = user.id;
        updateFields.approved_at = new Date().toISOString();
        updateFields.subscription_status = 'active';
        break;
      case 'update':
        updateFields = { ...updateFields, ...updateData };
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

    // Update doctor
    const { data: updatedDoctor, error: updateError } = await supabase
      .from('doctors')
      .update(updateFields)
      .eq('id', doctorId)
      .select()
      .single();

    if (updateError) {
      console.error('Update error:', updateError);
      return NextResponse.json(
        { error: 'Failed to update doctor' },
        { status: 500 }
      );
    }

    // Log the action
    await supabase
      .from('audit_logs')
      .insert({
        user_id: user.id,
        action: `doctor_${action}`,
        resource_type: 'doctor',
        resource_id: doctorId,
        details: updateFields,
        created_at: new Date().toISOString()
      });

    return NextResponse.json({
      success: true,
      doctor: updatedDoctor
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
