import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { Database } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    // Create Supabase client with anon key to get current session
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll() {},
        },
      }
    );

    // Also create service role client for admin queries
    const supabaseAdmin = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        cookies: {
          getAll() { return []; },
          setAll() {},
        },
      }
    );

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return NextResponse.json({
        error: 'No authenticated user',
        userError: userError?.message
      });
    }

    // Check user metadata
    const userMetadata = {
      user_metadata: user.user_metadata,
      app_metadata: user.app_metadata,
      email: user.email,
      id: user.id
    };

    // Check platform_admins table using service role
    const { data: adminData, error: adminError } = await supabaseAdmin
      .from('platform_admins')
      .select('*')
      .eq('id', user.id)
      .single();

    // Check platform_admins by email
    const { data: adminByEmail, error: adminEmailError } = await supabaseAdmin
      .from('platform_admins')
      .select('*')
      .eq('email', user.email)
      .single();

    // Check doctors table
    const { data: doctorData, error: doctorError } = await supabaseAdmin
      .from('doctors')
      .select('*')
      .eq('id', user.id)
      .single();

    return NextResponse.json({
      user: userMetadata,
      adminById: { data: adminData, error: adminError?.message },
      adminByEmail: { data: adminByEmail, error: adminEmailError?.message },
      doctor: { data: doctorData, error: doctorError?.message },
      detectedRole: adminData ? 'super_admin' : doctorData ? 'doctor' : 'unknown'
    });

  } catch (error) {
    console.error('Debug error:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
