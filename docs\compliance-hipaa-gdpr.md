# 🔒 HIPAA/GDPR Compliance Documentation

## Overview
This document outlines the comprehensive compliance measures implemented in the DentoPro SaaS platform to meet HIPAA (Health Insurance Portability and Accountability Act) and GDPR (General Data Protection Regulation) requirements.

## HIPAA Compliance

### 1. Administrative Safeguards

#### Security Officer
- **Designated Security Officer**: Platform administrator responsible for HIPAA compliance
- **Workforce Training**: All personnel trained on HIPAA requirements
- **Access Management**: Role-based access control with minimum necessary access
- **Incident Response**: Documented procedures for security incidents

#### Policies and Procedures
```typescript
// Access control policy implementation
export const AccessControlPolicy = {
  // Minimum necessary access
  getDoctorAccess: (doctorId: string) => ({
    patients: { doctor_id: doctorId },
    appointments: { doctor_id: doctorId },
    treatments: { doctor_id: doctorId }
  }),
  
  // Patient access restrictions
  getPatientAccess: (patientId: string) => ({
    patients: { id: patientId },
    appointments: { patient_id: patientId },
    treatments: { patient_id: patientId }
  }),
  
  // Audit logging
  logAccess: (userId: string, resource: string, action: string) => {
    console.log(`[HIPAA AUDIT] User: ${userId}, Resource: ${resource}, Action: ${action}, Time: ${new Date().toISOString()}`);
  }
};
```

### 2. Physical Safeguards

#### Data Center Security
- **Secure Hosting**: Cloud infrastructure with SOC 2 Type II compliance
- **Physical Access Controls**: Biometric access controls at data centers
- **Environmental Controls**: Temperature, humidity, and fire suppression systems
- **Equipment Disposal**: Secure destruction of storage media

#### Workstation Security
```typescript
// Workstation security requirements
export const WorkstationSecurity = {
  // Session timeout
  SESSION_TIMEOUT: 15 * 60 * 1000, // 15 minutes
  
  // Automatic logout
  setupAutoLogout: () => {
    let lastActivity = Date.now();
    
    document.addEventListener('mousemove', () => {
      lastActivity = Date.now();
    });
    
    setInterval(() => {
      if (Date.now() - lastActivity > WorkstationSecurity.SESSION_TIMEOUT) {
        // Automatic logout
        window.location.href = '/logout';
      }
    }, 60000); // Check every minute
  },
  
  // Screen lock detection
  detectScreenLock: () => {
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        // Lock session when screen is hidden
        sessionStorage.setItem('session_locked', 'true');
      }
    });
  }
};
```

### 3. Technical Safeguards

#### Access Control
```sql
-- HIPAA-compliant access control
CREATE OR REPLACE FUNCTION hipaa_access_control(
  user_id UUID,
  user_role TEXT,
  resource_type TEXT,
  resource_id UUID,
  action TEXT
) RETURNS BOOLEAN AS $$
DECLARE
  access_granted BOOLEAN := FALSE;
  doctor_id UUID;
  patient_id UUID;
BEGIN
  -- Log access attempt
  INSERT INTO system_logs (level, message, user_id, user_type, action, resource_type, resource_id)
  VALUES ('info', 'Access attempt', user_id, user_role, action, resource_type, resource_id);
  
  -- Super admin has full access
  IF user_role = 'super_admin' THEN
    access_granted := TRUE;
  
  -- Doctor access to their own data
  ELSIF user_role = 'doctor' THEN
    SELECT id INTO doctor_id FROM doctors WHERE id = user_id;
    
    IF resource_type IN ('patients', 'appointments', 'treatments', 'materials', 'prescriptions', 'invoices') THEN
      -- Check if resource belongs to doctor
      EXECUTE format('SELECT doctor_id FROM %I WHERE id = $1', resource_type) 
      INTO doctor_id USING resource_id;
      
      IF doctor_id = user_id THEN
        access_granted := TRUE;
      END IF;
    END IF;
  
  -- Patient access to their own data only
  ELSIF user_role = 'patient' THEN
    SELECT id INTO patient_id FROM patients WHERE id = user_id;
    
    IF resource_type = 'patients' AND resource_id = patient_id THEN
      access_granted := TRUE;
    ELSIF resource_type IN ('appointments', 'treatments', 'prescriptions', 'invoices') THEN
      -- Check if resource belongs to patient
      EXECUTE format('SELECT patient_id FROM %I WHERE id = $1', resource_type) 
      INTO patient_id USING resource_id;
      
      IF patient_id = user_id THEN
        access_granted := TRUE;
      END IF;
    END IF;
  END IF;
  
  -- Log access result
  INSERT INTO system_logs (level, message, user_id, user_type, action, resource_type, resource_id, metadata)
  VALUES ('info', 'Access ' || CASE WHEN access_granted THEN 'granted' ELSE 'denied' END, 
          user_id, user_role, action, resource_type, resource_id, 
          jsonb_build_object('access_granted', access_granted));
  
  RETURN access_granted;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### Audit Controls
```sql
-- Comprehensive audit logging
CREATE TABLE hipaa_audit_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  user_role TEXT NOT NULL,
  action TEXT NOT NULL,
  resource_type TEXT NOT NULL,
  resource_id UUID,
  patient_id UUID, -- For patient data access tracking
  doctor_id UUID,  -- For doctor context
  ip_address INET,
  user_agent TEXT,
  session_id TEXT,
  access_granted BOOLEAN,
  failure_reason TEXT,
  phi_accessed BOOLEAN DEFAULT FALSE, -- Protected Health Information flag
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Function to log PHI access
CREATE OR REPLACE FUNCTION log_phi_access(
  p_user_id UUID,
  p_user_role TEXT,
  p_action TEXT,
  p_resource_type TEXT,
  p_resource_id UUID,
  p_patient_id UUID DEFAULT NULL,
  p_doctor_id UUID DEFAULT NULL
) RETURNS void AS $$
BEGIN
  INSERT INTO hipaa_audit_log (
    user_id, user_role, action, resource_type, resource_id,
    patient_id, doctor_id, phi_accessed, ip_address, user_agent, session_id
  ) VALUES (
    p_user_id, p_user_role, p_action, p_resource_type, p_resource_id,
    p_patient_id, p_doctor_id, TRUE,
    inet_client_addr(), current_setting('application_name'), 
    current_setting('myapp.session_id', true)
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### Data Integrity
```typescript
// Data integrity verification
export class DataIntegrity {
  // Generate data hash for integrity checking
  static generateDataHash(data: any): string {
    const crypto = require('crypto');
    const jsonString = JSON.stringify(data, Object.keys(data).sort());
    return crypto.createHash('sha256').update(jsonString).digest('hex');
  }
  
  // Verify data integrity
  static verifyDataIntegrity(data: any, expectedHash: string): boolean {
    const currentHash = this.generateDataHash(data);
    return currentHash === expectedHash;
  }
  
  // Add integrity check to patient records
  static addIntegrityCheck(patientData: any): any {
    const dataWithoutHash = { ...patientData };
    delete dataWithoutHash.integrity_hash;
    
    return {
      ...patientData,
      integrity_hash: this.generateDataHash(dataWithoutHash),
      last_verified: new Date().toISOString()
    };
  }
}
```

#### Transmission Security
```typescript
// Secure data transmission
export class TransmissionSecurity {
  // Encrypt data for transmission
  static encryptForTransmission(data: any): string {
    const { encrypt } = require('./encryption');
    return encrypt(JSON.stringify(data));
  }
  
  // Decrypt received data
  static decryptFromTransmission(encryptedData: string): any {
    const { decrypt } = require('./encryption');
    return JSON.parse(decrypt(encryptedData));
  }
  
  // Secure API communication
  static setupSecureHeaders(req: any, res: any, next: any) {
    // HTTPS enforcement
    if (process.env.NODE_ENV === 'production' && !req.secure) {
      return res.redirect(301, `https://${req.headers.host}${req.url}`);
    }
    
    // Security headers
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    
    next();
  }
}
```

## GDPR Compliance

### 1. Data Subject Rights

#### Right to Access
```typescript
// Data export for GDPR compliance
export class GDPRDataExport {
  static async exportPatientData(patientId: string): Promise<any> {
    const db = require('./database').default;
    
    // Get all patient data
    const patientData = await db.findOne('patients', { id: patientId });
    const appointments = await db.findMany('appointments', { patient_id: patientId });
    const treatments = await db.findMany('treatments', { patient_id: patientId });
    const prescriptions = await db.findMany('prescriptions', { patient_id: patientId });
    const invoices = await db.findMany('invoices', { patient_id: patientId });
    const files = await db.findMany('file_uploads', { patient_id: patientId });
    
    // Decrypt sensitive data
    const { DatabaseEncryption } = require('./encryption');
    const decryptedPatient = DatabaseEncryption.decryptPatientData(patientData);
    
    return {
      personal_data: decryptedPatient,
      appointments: appointments,
      treatments: treatments,
      prescriptions: prescriptions,
      invoices: invoices,
      files: files.map(f => ({ name: f.file_name, type: f.file_type, uploaded: f.created_at })),
      export_date: new Date().toISOString(),
      data_controller: 'DentoPro SaaS Platform'
    };
  }
}
```

#### Right to Rectification
```typescript
// Data correction tracking
export class DataRectification {
  static async updatePatientData(patientId: string, updates: any, userId: string): Promise<void> {
    const db = require('./database').default;
    
    // Log the change for audit
    await db.insert('data_changes_log', {
      patient_id: patientId,
      changed_by: userId,
      changes: JSON.stringify(updates),
      change_type: 'rectification',
      legal_basis: 'GDPR Article 16 - Right to rectification',
      created_at: new Date()
    });
    
    // Update the data
    await db.update('patients', updates, { id: patientId });
  }
}
```

#### Right to Erasure (Right to be Forgotten)
```sql
-- Data deletion with audit trail
CREATE OR REPLACE FUNCTION gdpr_delete_patient_data(
  p_patient_id UUID,
  p_requested_by UUID,
  p_reason TEXT
) RETURNS void AS $$
DECLARE
  deletion_id UUID;
BEGIN
  -- Create deletion record
  INSERT INTO data_deletions (id, patient_id, requested_by, reason, status, created_at)
  VALUES (gen_random_uuid(), p_patient_id, p_requested_by, p_reason, 'in_progress', CURRENT_TIMESTAMP)
  RETURNING id INTO deletion_id;
  
  -- Anonymize patient data (keep for medical/legal requirements)
  UPDATE patients SET
    full_name = 'DELETED_' || id,
    email = NULL,
    phone = NULL,
    address = NULL,
    notes = 'Data deleted per GDPR request',
    is_deleted = TRUE,
    deleted_at = CURRENT_TIMESTAMP
  WHERE id = p_patient_id;
  
  -- Delete or anonymize related data
  UPDATE appointments SET notes = 'Patient data deleted' WHERE patient_id = p_patient_id;
  UPDATE treatments SET notes = 'Patient data deleted' WHERE patient_id = p_patient_id;
  
  -- Mark files for deletion
  UPDATE file_uploads SET is_deleted = TRUE WHERE patient_id = p_patient_id;
  
  -- Complete deletion record
  UPDATE data_deletions SET status = 'completed', completed_at = CURRENT_TIMESTAMP
  WHERE id = deletion_id;
  
  -- Log the deletion
  INSERT INTO system_logs (level, message, user_id, action, resource_type, resource_id)
  VALUES ('info', 'GDPR data deletion completed', p_requested_by, 'delete', 'patients', p_patient_id);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### Data Portability
```typescript
// Data portability in machine-readable format
export class DataPortability {
  static async exportPatientDataPortable(patientId: string): Promise<any> {
    const data = await GDPRDataExport.exportPatientData(patientId);
    
    // Convert to standardized format (FHIR-like structure)
    return {
      resourceType: 'Patient',
      id: patientId,
      identifier: [{ value: patientId }],
      name: [{ text: data.personal_data.full_name }],
      telecom: [
        { system: 'phone', value: data.personal_data.phone },
        { system: 'email', value: data.personal_data.email }
      ],
      birthDate: data.personal_data.date_of_birth,
      gender: data.personal_data.gender,
      address: [{ text: data.personal_data.address }],
      appointments: data.appointments,
      treatments: data.treatments,
      prescriptions: data.prescriptions,
      invoices: data.invoices,
      exportMetadata: {
        exportDate: data.export_date,
        format: 'FHIR-like',
        version: '1.0'
      }
    };
  }
}
```

### 2. Privacy by Design

#### Data Minimization
```typescript
// Data minimization principles
export class DataMinimization {
  // Only collect necessary data
  static validateDataCollection(data: any, purpose: string): any {
    const allowedFields = {
      'patient_registration': ['full_name', 'phone', 'email', 'date_of_birth', 'gender'],
      'appointment_booking': ['patient_id', 'appointment_date', 'appointment_time', 'type'],
      'treatment_record': ['patient_id', 'procedure', 'notes', 'cost']
    };
    
    const allowed = allowedFields[purpose] || [];
    const filtered = {};
    
    for (const field of allowed) {
      if (data[field] !== undefined) {
        filtered[field] = data[field];
      }
    }
    
    return filtered;
  }
  
  // Automatic data retention
  static async enforceDataRetention(): Promise<void> {
    const db = require('./database').default;
    
    // Delete old audit logs (keep for 7 years)
    await db.query(`
      DELETE FROM system_logs 
      WHERE created_at < CURRENT_DATE - INTERVAL '7 years'
    `);
    
    // Archive old patient data (inactive for 10 years)
    await db.query(`
      UPDATE patients SET is_archived = TRUE 
      WHERE last_visit < CURRENT_DATE - INTERVAL '10 years'
      AND is_archived = FALSE
    `);
  }
}
```

### 3. Consent Management

```sql
-- Consent tracking table
CREATE TABLE consent_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  patient_id UUID NOT NULL REFERENCES patients(id),
  consent_type TEXT NOT NULL, -- 'data_processing', 'marketing', 'research'
  consent_given BOOLEAN NOT NULL,
  consent_date TIMESTAMP NOT NULL,
  consent_method TEXT NOT NULL, -- 'online_form', 'verbal', 'written'
  legal_basis TEXT NOT NULL, -- GDPR legal basis
  purpose TEXT NOT NULL,
  data_categories TEXT[], -- Types of data covered
  retention_period TEXT,
  withdrawn_date TIMESTAMP,
  withdrawal_method TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Function to record consent
CREATE OR REPLACE FUNCTION record_consent(
  p_patient_id UUID,
  p_consent_type TEXT,
  p_consent_given BOOLEAN,
  p_method TEXT,
  p_legal_basis TEXT,
  p_purpose TEXT,
  p_data_categories TEXT[]
) RETURNS UUID AS $$
DECLARE
  consent_id UUID;
BEGIN
  INSERT INTO consent_records (
    patient_id, consent_type, consent_given, consent_date,
    consent_method, legal_basis, purpose, data_categories
  ) VALUES (
    p_patient_id, p_consent_type, p_consent_given, CURRENT_TIMESTAMP,
    p_method, p_legal_basis, p_purpose, p_data_categories
  ) RETURNING id INTO consent_id;
  
  RETURN consent_id;
END;
$$ LANGUAGE plpgsql;
```

## Compliance Monitoring

### 1. Automated Compliance Checks
```typescript
// Automated compliance monitoring
export class ComplianceMonitoring {
  static async runDailyComplianceCheck(): Promise<any> {
    const issues = [];
    
    // Check for unencrypted sensitive data
    const unencryptedData = await this.checkUnencryptedData();
    if (unencryptedData.length > 0) {
      issues.push({ type: 'encryption', severity: 'high', count: unencryptedData.length });
    }
    
    // Check for expired consents
    const expiredConsents = await this.checkExpiredConsents();
    if (expiredConsents.length > 0) {
      issues.push({ type: 'consent', severity: 'medium', count: expiredConsents.length });
    }
    
    // Check for data retention violations
    const retentionViolations = await this.checkDataRetention();
    if (retentionViolations.length > 0) {
      issues.push({ type: 'retention', severity: 'high', count: retentionViolations.length });
    }
    
    // Generate compliance report
    return {
      date: new Date().toISOString(),
      status: issues.length === 0 ? 'compliant' : 'issues_found',
      issues: issues,
      next_check: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
    };
  }
}
```

This comprehensive compliance framework ensures that the DentoPro platform meets all HIPAA and GDPR requirements while maintaining operational efficiency and user experience.
