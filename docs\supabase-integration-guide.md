# 🚀 Complete Supabase Integration Guide for DentoPro

## Overview
This guide provides step-by-step instructions for integrating Supabase into the DentoPro SaaS platform, including database migration, authentication setup, and real-time features.

## Phase 1: Supabase Project Setup

### Step 1: Create Supabase Project
1. **Go to Supabase Dashboard**
   - Visit: https://supabase.com/dashboard
   - Sign in with your account or create a new one

2. **Create New Project**
   - Click "New Project"
   - Organization: Select or create organization
   - Project Name: `dentopro-saas`
   - Database Password: Generate strong password (save securely)
   - Region: Choose closest to your users
   - Pricing Plan: Start with Free tier for development

3. **Wait for Project Creation**
   - Project setup takes 2-3 minutes
   - Note down your project URL and keys

### Step 2: Get Supabase Credentials
After project creation, collect these credentials from Setting<PERSON> > API:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
SUPABASE_JWT_SECRET=your-jwt-secret
SUPABASE_DB_PASSWORD=your-database-password

# Database Direct Connection (for migrations)
SUPABASE_DB_HOST=db.your-project.supabase.co
SUPABASE_DB_PORT=5432
SUPABASE_DB_NAME=postgres
SUPABASE_DB_USER=postgres
```

### Step 3: Configure Authentication Providers
1. **Go to Authentication > Providers**
2. **Enable Google OAuth**
   - Toggle Google provider ON
   - Add Google Client ID and Secret
   - Set redirect URL: `https://your-project.supabase.co/auth/v1/callback`

3. **Configure Email Settings**
   - Go to Authentication > Settings
   - Configure SMTP settings for email verification
   - Set site URL and redirect URLs

## Phase 2: Install Supabase Dependencies

### Install Required Packages
```bash
npm install @supabase/supabase-js @supabase/auth-helpers-nextjs @supabase/auth-helpers-react @supabase/auth-ui-react @supabase/auth-ui-shared
```

### Development Dependencies
```bash
npm install --save-dev supabase
```

## Phase 3: Database Migration to Supabase

### Step 1: Initialize Supabase CLI
```bash
# Install Supabase CLI globally
npm install -g supabase

# Login to Supabase
supabase login

# Initialize project
supabase init

# Link to your project
supabase link --project-ref your-project-ref
```

### Step 2: Migrate Existing Schema
1. **Create Migration Files**
   ```bash
   # Create new migration
   supabase migration new initial_schema
   ```

2. **Copy Our Schema to Supabase Migration**
   - Copy content from `database/migrations/001_initial_schema.sql`
   - Paste into the new Supabase migration file
   - Repeat for other migration files

3. **Apply Migrations**
   ```bash
   # Push migrations to Supabase
   supabase db push
   
   # Or apply specific migration
   supabase migration up
   ```

### Step 3: Configure Row Level Security
Supabase automatically enables RLS. Our existing RLS policies need to be adapted:

```sql
-- Enable RLS on all tables (already done in Supabase)
-- Our existing policies from 003_row_level_security.sql will work
-- But need to adapt auth.jwt() to auth.uid() and auth.jwt()

-- Example adapted policy:
CREATE POLICY "Doctors can only access their own patients" ON patients
FOR ALL USING (
  auth.jwt() ->> 'role' = 'doctor' AND 
  doctor_id = (auth.jwt() ->> 'doctor_id')::UUID
);
```

## Phase 4: Supabase Client Configuration

### Create Supabase Client
```typescript
// src/lib/supabase.ts
import { createClient } from '@supabase/supabase-js'
import { Database } from '@/types/supabase'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey)

// Server-side client with service role
export const supabaseAdmin = createClient<Database>(
  supabaseUrl,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)
```

### Create Database Types
```bash
# Generate TypeScript types from Supabase schema
supabase gen types typescript --project-id your-project-ref > src/types/supabase.ts
```

## Phase 5: Authentication Integration

### Setup Auth Helpers
```typescript
// src/lib/auth.ts
import { createServerComponentClient, createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { Database } from '@/types/supabase'

// Server component auth
export const createServerSupabaseClient = () => {
  const cookieStore = cookies()
  return createServerComponentClient<Database>({ cookies: () => cookieStore })
}

// Client component auth
export const createClientSupabaseClient = () => {
  return createClientComponentClient<Database>()
}
```

### Auth Context Provider
```typescript
// src/contexts/AuthContext.tsx
'use client'
import { createContext, useContext, useEffect, useState } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { createClientSupabaseClient } from '@/lib/auth'

interface AuthContextType {
  user: User | null
  session: Session | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<any>
  signInWithGoogle: () => Promise<any>
  signOut: () => Promise<any>
  signUp: (email: string, password: string, metadata?: any) => Promise<any>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  
  const supabase = createClientSupabaseClient()

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session)
      setUser(session?.user ?? null)
      setLoading(false)
    })

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session)
        setUser(session?.user ?? null)
        setLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [supabase.auth])

  const signIn = async (email: string, password: string) => {
    return await supabase.auth.signInWithPassword({ email, password })
  }

  const signInWithGoogle = async () => {
    return await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/auth/callback`
      }
    })
  }

  const signOut = async () => {
    return await supabase.auth.signOut()
  }

  const signUp = async (email: string, password: string, metadata?: any) => {
    return await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata
      }
    })
  }

  return (
    <AuthContext.Provider value={{
      user,
      session,
      loading,
      signIn,
      signInWithGoogle,
      signOut,
      signUp
    }}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider')
  }
  return context
}
```

## Phase 6: Real-time Features

### Setup Real-time Subscriptions
```typescript
// src/hooks/useRealtimeSubscription.ts
import { useEffect, useState } from 'react'
import { createClientSupabaseClient } from '@/lib/auth'
import { RealtimeChannel } from '@supabase/supabase-js'

export function useRealtimeSubscription<T>(
  table: string,
  filter?: string,
  doctorId?: string
) {
  const [data, setData] = useState<T[]>([])
  const [loading, setLoading] = useState(true)
  const supabase = createClientSupabaseClient()

  useEffect(() => {
    let channel: RealtimeChannel

    const setupSubscription = async () => {
      // Initial data fetch
      let query = supabase.from(table).select('*')
      
      if (doctorId) {
        query = query.eq('doctor_id', doctorId)
      }
      
      if (filter) {
        // Apply additional filters
      }

      const { data: initialData } = await query
      setData(initialData || [])
      setLoading(false)

      // Setup real-time subscription
      channel = supabase
        .channel(`${table}_changes`)
        .on('postgres_changes', 
          { 
            event: '*', 
            schema: 'public', 
            table: table,
            filter: doctorId ? `doctor_id=eq.${doctorId}` : undefined
          }, 
          (payload) => {
            if (payload.eventType === 'INSERT') {
              setData(prev => [...prev, payload.new as T])
            } else if (payload.eventType === 'UPDATE') {
              setData(prev => prev.map(item => 
                (item as any).id === payload.new.id ? payload.new as T : item
              ))
            } else if (payload.eventType === 'DELETE') {
              setData(prev => prev.filter(item => 
                (item as any).id !== payload.old.id
              ))
            }
          }
        )
        .subscribe()
    }

    setupSubscription()

    return () => {
      if (channel) {
        supabase.removeChannel(channel)
      }
    }
  }, [table, filter, doctorId, supabase])

  return { data, loading }
}
```

## Phase 7: File Storage Integration

### Setup Supabase Storage
```typescript
// src/lib/storage.ts
import { createClientSupabaseClient } from '@/lib/auth'

export class SupabaseStorage {
  private supabase = createClientSupabaseClient()

  async uploadFile(
    bucket: string,
    path: string,
    file: File,
    options?: { upsert?: boolean }
  ) {
    const { data, error } = await this.supabase.storage
      .from(bucket)
      .upload(path, file, options)

    if (error) throw error
    return data
  }

  async downloadFile(bucket: string, path: string) {
    const { data, error } = await this.supabase.storage
      .from(bucket)
      .download(path)

    if (error) throw error
    return data
  }

  async getPublicUrl(bucket: string, path: string) {
    const { data } = this.supabase.storage
      .from(bucket)
      .getPublicUrl(path)

    return data.publicUrl
  }

  async deleteFile(bucket: string, path: string) {
    const { error } = await this.supabase.storage
      .from(bucket)
      .remove([path])

    if (error) throw error
  }

  // Create storage buckets
  async createBucket(name: string, options?: any) {
    const { data, error } = await this.supabase.storage
      .createBucket(name, options)

    if (error) throw error
    return data
  }
}

export const storage = new SupabaseStorage()
```

## Phase 8: Environment Configuration

### Update Environment Variables
```env
# Replace existing database config with Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
SUPABASE_JWT_SECRET=your-jwt-secret

# Keep other existing variables
NEXT_PUBLIC_APP_NAME=DentoPro
NEXT_PUBLIC_APP_URL=http://localhost:3000
STRIPE_PUBLISHABLE_KEY=pk_test_your-key
STRIPE_SECRET_KEY=sk_test_your-key
TWILIO_ACCOUNT_SID=your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-token
```

## Next Steps After Integration

1. **Test Authentication Flow**
2. **Migrate Existing Database Utilities**
3. **Implement Real-time Features**
4. **Set up File Storage Buckets**
5. **Configure RLS Policies**
6. **Test Multi-tenant Isolation**

This integration will give us:
- ✅ Managed PostgreSQL database
- ✅ Built-in authentication with Google OAuth
- ✅ Real-time subscriptions
- ✅ File storage and CDN
- ✅ Automatic API generation
- ✅ Built-in security with RLS
- ✅ Scalable infrastructure
