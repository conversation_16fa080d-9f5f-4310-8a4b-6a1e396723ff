// Two-Factor Authentication utilities
import crypto from 'crypto';

// Twilio configuration (you can replace with any SMS service)
interface TwilioConfig {
  accountSid: string;
  authToken: string;
  phoneNumber: string;
}

// SMS Service interface for flexibility
interface SMSService {
  sendSMS(to: string, message: string): Promise<void>;
}

// Twilio SMS implementation
class TwilioSMSService implements SMSService {
  private config: TwilioConfig;

  constructor(config: TwilioConfig) {
    this.config = config;
  }

  async sendSMS(to: string, message: string): Promise<void> {
    try {
      // In a real implementation, you would use the Twilio SDK
      // For now, this is a placeholder that logs the message
      console.log(`SMS to ${to}: ${message}`);
      
      // Uncomment and configure when you have Twilio credentials:
      /*
      const twilio = require('twilio');
      const client = twilio(this.config.accountSid, this.config.authToken);
      
      await client.messages.create({
        body: message,
        from: this.config.phoneNumber,
        to: to
      });
      */
      
      // For development, we'll simulate success
      return Promise.resolve();
    } catch (error) {
      console.error('SMS sending failed:', error);
      throw new Error('Failed to send SMS');
    }
  }
}

// Mock SMS service for development/testing
class MockSMSService implements SMSService {
  async sendSMS(to: string, message: string): Promise<void> {
    console.log(`[MOCK SMS] To: ${to}, Message: ${message}`);
    return Promise.resolve();
  }
}

// Two-Factor Authentication main class
export class TwoFactorAuth {
  private static smsService: SMSService;

  // Initialize SMS service
  static initialize() {
    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    const phoneNumber = process.env.TWILIO_PHONE_NUMBER;

    if (accountSid && authToken && phoneNumber) {
      this.smsService = new TwilioSMSService({
        accountSid,
        authToken,
        phoneNumber
      });
    } else {
      console.warn('Twilio credentials not found, using mock SMS service');
      this.smsService = new MockSMSService();
    }
  }

  // Generate a 6-digit verification code
  static generateCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  // Generate a cryptographically secure code
  static generateSecureCode(): string {
    const buffer = crypto.randomBytes(3);
    const code = parseInt(buffer.toString('hex'), 16) % 1000000;
    return code.toString().padStart(6, '0');
  }

  // Send verification code via SMS
  static async sendSMS(phoneNumber: string, code: string): Promise<void> {
    if (!this.smsService) {
      this.initialize();
    }

    const message = `Your DentoPro verification code is: ${code}. This code expires in 10 minutes. Do not share this code with anyone.`;
    
    await this.smsService.sendSMS(phoneNumber, message);
  }

  // Validate phone number format
  static validatePhoneNumber(phoneNumber: string): boolean {
    // International format: +[country code][number]
    const phoneRegex = /^\+[1-9]\d{1,14}$/;
    return phoneRegex.test(phoneNumber);
  }

  // Format phone number for display (mask middle digits)
  static maskPhoneNumber(phoneNumber: string): string {
    if (phoneNumber.length < 8) {
      return phoneNumber;
    }
    
    const countryCode = phoneNumber.substring(0, phoneNumber.length - 7);
    const lastFour = phoneNumber.substring(phoneNumber.length - 4);
    const masked = '*'.repeat(phoneNumber.length - countryCode.length - 4);
    
    return `${countryCode}${masked}${lastFour}`;
  }

  // Verify code timing (prevent timing attacks)
  static async verifyCode(providedCode: string, actualCode: string): Promise<boolean> {
    // Use crypto.timingSafeEqual to prevent timing attacks
    if (providedCode.length !== actualCode.length) {
      return false;
    }

    const providedBuffer = Buffer.from(providedCode, 'utf8');
    const actualBuffer = Buffer.from(actualCode, 'utf8');

    try {
      return crypto.timingSafeEqual(providedBuffer, actualBuffer);
    } catch {
      return false;
    }
  }

  // Generate backup codes for account recovery
  static generateBackupCodes(count: number = 8): string[] {
    const codes: string[] = [];
    
    for (let i = 0; i < count; i++) {
      // Generate 8-character alphanumeric backup code
      const code = crypto.randomBytes(4).toString('hex').toUpperCase();
      codes.push(code);
    }
    
    return codes;
  }

  // Rate limiting helper
  static createRateLimiter() {
    const attempts = new Map<string, { count: number; resetTime: number }>();
    
    return {
      isAllowed: (identifier: string, maxAttempts: number = 5, windowMs: number = 15 * 60 * 1000): boolean => {
        const now = Date.now();
        const userAttempts = attempts.get(identifier);
        
        if (!userAttempts || now > userAttempts.resetTime) {
          attempts.set(identifier, { count: 1, resetTime: now + windowMs });
          return true;
        }
        
        if (userAttempts.count >= maxAttempts) {
          return false;
        }
        
        userAttempts.count++;
        return true;
      },
      
      reset: (identifier: string): void => {
        attempts.delete(identifier);
      }
    };
  }
}

// Initialize on module load
TwoFactorAuth.initialize();

// Export types for use in other files
export interface TwoFactorSettings {
  userId: string;
  phoneNumber: string;
  isEnabled: boolean;
  backupCodes?: string[];
  enabledAt?: Date;
  lastUsedAt?: Date;
}

export interface VerificationCode {
  code: string;
  expiresAt: Date;
  attempts: number;
  maxAttempts: number;
}
