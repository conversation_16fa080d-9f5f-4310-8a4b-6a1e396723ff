import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { Database } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    // Create Supabase client with service role
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        cookies: {
          getAll() { return []; },
          setAll() {},
        },
      }
    );

    // Get the admin user from Auth by email
    const { data: { users }, error: listError } = await supabase.auth.admin.listUsers();
    
    if (listError) {
      return NextResponse.json({ error: 'Failed to list users', details: listError.message });
    }

    const adminUser = users.find(user => user.email === '<EMAIL>');
    
    if (!adminUser) {
      return NextResponse.json({ error: 'Admin user not found in Auth' });
    }

    // Get the current platform_admins record
    const { data: currentAdmin, error: fetchError } = await supabase
      .from('platform_admins')
      .select('*')
      .eq('email', '<EMAIL>')
      .single();

    if (fetchError || !currentAdmin) {
      return NextResponse.json({ error: 'Admin record not found in platform_admins table' });
    }

    // Delete the old record
    const { error: deleteError } = await supabase
      .from('platform_admins')
      .delete()
      .eq('email', '<EMAIL>');

    if (deleteError) {
      return NextResponse.json({ error: 'Failed to delete old admin record', details: deleteError.message });
    }

    // Create new record with correct Auth user ID
    const { data: newAdmin, error: insertError } = await supabase
      .from('platform_admins')
      .insert({
        id: adminUser.id, // Use the correct Auth user ID
        email: currentAdmin.email,
        password_hash: currentAdmin.password_hash,
        full_name: currentAdmin.full_name,
        role: currentAdmin.role,
        permissions: currentAdmin.permissions,
        is_active: currentAdmin.is_active,
        created_at: currentAdmin.created_at,
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (insertError) {
      return NextResponse.json({ error: 'Failed to create new admin record', details: insertError.message });
    }

    return NextResponse.json({
      success: true,
      message: 'Admin ID fixed successfully',
      oldId: currentAdmin.id,
      newId: adminUser.id,
      authUserId: adminUser.id,
      adminRecord: newAdmin
    });

  } catch (error) {
    console.error('Fix admin ID error:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
