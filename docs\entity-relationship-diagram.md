# 🔗 DentoPro Entity Relationship Diagram (ERD)

## Overview
This document describes the relationships between all entities in the DentoPro SaaS platform database.

## Entity Relationship Structure

### 🏢 **Platform Level (Super Admin)**
```
platform_admins (Super Admin Management)
    ↓ (1:many)
doctors (Doctor Approval & Management)
    ↓ (1:many)
subscriptions (Billing & Revenue)
    ↓ (1:many)
platform_analytics (Usage Metrics)
    ↓ (1:many)
license_tracking (Anti-Resale Protection)
```

### 🏥 **Clinic Level (Doctor Management)**
```
doctors
    ↓ (1:1)
clinic_settings (Clinic Configuration)

doctors
    ↓ (1:many)
patients (Patient Records)
    ↓ (1:many)
appointments (Scheduling)
        ↓ (1:many)
        treatments (Clinical Records)
        ↓ (1:many)
        prescriptions (e-Prescriptions)
        ↓ (1:many)
        invoices (Billing)

doctors
    ↓ (1:many)
materials (Inventory)
    ↓ (1:many)
material_logs (Stock Movements)

doctors
    ↓ (1:many)
file_uploads (Document Management)

doctors
    ↓ (1:many)
notifications (Communication)

doctors
    ↓ (1:many)
system_logs (Audit Trail)
```

## Detailed Relationships

### 1. Platform Management Relationships

#### platform_admins → doctors
- **Type**: One-to-Many
- **Description**: Super admins approve and manage doctor accounts
- **Foreign Key**: `doctors.approved_by → platform_admins.id`
- **Business Rule**: Only approved doctors can access the system

#### doctors → subscriptions
- **Type**: One-to-Many
- **Description**: Each doctor can have multiple subscription records (history)
- **Foreign Key**: `subscriptions.doctor_id → doctors.id`
- **Business Rule**: Active subscription required for system access

#### doctors → platform_analytics
- **Type**: One-to-Many
- **Description**: Usage metrics tracked per doctor
- **Foreign Key**: `platform_analytics.doctor_id → doctors.id`
- **Business Rule**: Analytics help optimize platform performance

#### doctors → license_tracking
- **Type**: One-to-Many
- **Description**: Track doctor's system usage and prevent resale
- **Foreign Key**: `license_tracking.doctor_id → doctors.id`
- **Business Rule**: Domain binding prevents unauthorized access

### 2. Core Clinic Relationships

#### doctors → clinic_settings
- **Type**: One-to-One
- **Description**: Each doctor has unique clinic configuration
- **Foreign Key**: `clinic_settings.doctor_id → doctors.id`
- **Business Rule**: Settings control clinic behavior and preferences

#### doctors → patients
- **Type**: One-to-Many
- **Description**: Each doctor manages their own patients
- **Foreign Key**: `patients.doctor_id → doctors.id`
- **Business Rule**: Complete data isolation between doctors

#### patients → appointments
- **Type**: One-to-Many
- **Description**: Patients can have multiple appointments
- **Foreign Key**: `appointments.patient_id → patients.id`
- **Business Rule**: Appointments must belong to same doctor as patient

#### appointments → treatments
- **Type**: One-to-Many
- **Description**: Each appointment can have multiple treatments
- **Foreign Key**: `treatments.appointment_id → appointments.id`
- **Business Rule**: Treatments can exist without appointments (walk-ins)

#### appointments → prescriptions
- **Type**: One-to-Many
- **Description**: Prescriptions can be issued during appointments
- **Foreign Key**: `prescriptions.appointment_id → appointments.id`
- **Business Rule**: Prescriptions can exist without appointments

#### appointments → invoices
- **Type**: One-to-Many
- **Description**: Billing for appointment services
- **Foreign Key**: `invoices.appointment_id → appointments.id`
- **Business Rule**: Invoices can exist without appointments (products)

### 3. Inventory Management Relationships

#### doctors → materials
- **Type**: One-to-Many
- **Description**: Each doctor manages their own inventory
- **Foreign Key**: `materials.doctor_id → doctors.id`
- **Business Rule**: Inventory is completely isolated per doctor

#### materials → material_logs
- **Type**: One-to-Many
- **Description**: Track all stock movements for each material
- **Foreign Key**: `material_logs.material_id → materials.id`
- **Business Rule**: Complete audit trail for inventory changes

#### treatments → materials (via JSONB)
- **Type**: Many-to-Many
- **Description**: Treatments use multiple materials, materials used in multiple treatments
- **Implementation**: `treatments.materials_used` JSONB array
- **Business Rule**: Automatic stock deduction when treatment recorded

### 4. File Management Relationships

#### doctors → file_uploads
- **Type**: One-to-Many
- **Description**: All files belong to a specific doctor
- **Foreign Key**: `file_uploads.doctor_id → doctors.id`
- **Business Rule**: Files are isolated per doctor

#### patients → file_uploads
- **Type**: One-to-Many (Optional)
- **Description**: Files can be associated with specific patients
- **Foreign Key**: `file_uploads.patient_id → patients.id`
- **Business Rule**: Patient files only visible to their doctor

#### appointments → file_uploads
- **Type**: One-to-Many (Optional)
- **Description**: Files can be linked to specific appointments
- **Foreign Key**: `file_uploads.appointment_id → appointments.id`
- **Business Rule**: Appointment files help with treatment documentation

#### treatments → file_uploads
- **Type**: One-to-Many (Optional)
- **Description**: Before/after photos and treatment documentation
- **Foreign Key**: `file_uploads.treatment_id → treatments.id`
- **Business Rule**: Treatment files support clinical documentation

### 5. Communication Relationships

#### doctors → notifications
- **Type**: One-to-Many
- **Description**: Notifications sent to doctors about their clinic
- **Foreign Key**: `notifications.doctor_id → doctors.id`
- **Business Rule**: Doctors only see their own notifications

#### Multi-recipient notifications
- **Type**: Polymorphic
- **Description**: Notifications can be sent to different user types
- **Implementation**: `recipient_type` + `recipient_id`
- **Business Rule**: Flexible notification system for all user types

### 6. Audit and Monitoring Relationships

#### doctors → system_logs
- **Type**: One-to-Many
- **Description**: All doctor actions are logged
- **Foreign Key**: `system_logs.doctor_id → doctors.id`
- **Business Rule**: Complete audit trail for compliance

## Data Flow Patterns

### 1. Patient Journey Flow
```
Patient Registration → Patient Record
    ↓
Appointment Booking → Appointment Record
    ↓
Treatment Delivery → Treatment Record
    ↓
Material Usage → Material Log + Stock Update
    ↓
Prescription Issue → Prescription Record
    ↓
Billing → Invoice Record
    ↓
File Upload → File Record
```

### 2. Inventory Management Flow
```
Material Purchase → Material Log (stock_in)
    ↓
Stock Update → Material Record Update
    ↓
Treatment Delivery → Material Usage
    ↓
Auto Stock Deduction → Material Log (stock_out)
    ↓
Low Stock Alert → Notification Record
```

### 3. Analytics Flow
```
User Action → System Log
    ↓
Metric Calculation → Platform Analytics
    ↓
Usage Tracking → License Tracking
    ↓
Dashboard Display → Real-time Metrics
```

## Constraints and Business Rules

### Data Integrity Rules
1. **Doctor Isolation**: All patient data must belong to the same doctor
2. **Referential Integrity**: Foreign keys maintain data consistency
3. **Cascade Deletes**: Doctor deletion removes all related data
4. **Soft Deletes**: Important records marked inactive instead of deleted

### Security Rules
1. **Row Level Security**: Automatic data filtering by doctor_id
2. **Role-Based Access**: Different permissions for different user types
3. **Audit Trail**: All changes logged with user identification
4. **Data Encryption**: Sensitive fields encrypted at rest

### Business Logic Rules
1. **Subscription Validation**: Active subscription required for access
2. **License Binding**: Domain verification prevents unauthorized use
3. **Stock Management**: Automatic alerts for low inventory
4. **Appointment Conflicts**: Prevent double-booking time slots

## Performance Considerations

### Indexing Strategy
1. **Doctor-based indexes**: Primary isolation mechanism
2. **Date-based indexes**: Appointment and treatment queries
3. **Status indexes**: Filtering by record status
4. **Composite indexes**: Multi-column query optimization

### Query Optimization
1. **Partition by doctor_id**: Logical data separation
2. **Materialized views**: Pre-calculated summaries
3. **Connection pooling**: Efficient database connections
4. **Query caching**: Reduce database load

This ERD ensures complete data isolation, maintains referential integrity, and supports the multi-tenant SaaS architecture while providing comprehensive audit trails and security measures.
