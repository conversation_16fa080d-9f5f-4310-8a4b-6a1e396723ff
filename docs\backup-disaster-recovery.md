# 🔄 Backup & Disaster Recovery Plan

## Overview
This document outlines the comprehensive backup and disaster recovery strategy for the DentoPro SaaS platform, ensuring data protection, business continuity, and compliance with healthcare data regulations.

## Backup Strategy

### 1. Database Backups

#### Automated Daily Backups
```bash
# Full database backup (daily at 2 AM UTC)
pg_dump -h $DB_HOST -U $DB_USER -d dentopro_db \
  --format=custom \
  --compress=9 \
  --verbose \
  --file="/backups/daily/dentopro_$(date +%Y%m%d).backup"
```

#### Incremental Backups
```bash
# WAL (Write-Ahead Logging) continuous archiving
archive_mode = on
archive_command = 'cp %p /backups/wal/%f'
wal_level = replica
```

#### Per-Doctor Data Isolation Backups
```sql
-- Individual doctor data backup
COPY (
  SELECT * FROM patients WHERE doctor_id = $1
) TO '/backups/doctor_data/patients_$1.csv' WITH CSV HEADER;

-- Complete doctor clinic backup
pg_dump -h $DB_HOST -U $DB_USER -d dentopro_db \
  --table=patients \
  --table=appointments \
  --table=treatments \
  --table=materials \
  --table=prescriptions \
  --table=invoices \
  --where="doctor_id='$DOCTOR_ID'" \
  --file="/backups/doctors/$DOCTOR_ID_$(date +%Y%m%d).backup"
```

### 2. File Storage Backups

#### Patient Files and Documents
```bash
# Sync patient files to backup storage
rsync -avz --delete \
  /app/storage/uploads/ \
  /backups/files/uploads/

# Cloud backup to AWS S3
aws s3 sync /app/storage/uploads/ \
  s3://dentopro-backups/files/uploads/ \
  --delete --storage-class STANDARD_IA
```

#### Application Code and Configuration
```bash
# Git repository backup
git bundle create /backups/code/dentopro_$(date +%Y%m%d).bundle --all

# Configuration files backup
tar -czf /backups/config/config_$(date +%Y%m%d).tar.gz \
  /app/.env.production \
  /app/docker-compose.yml \
  /app/nginx.conf
```

### 3. Backup Retention Policy

#### Retention Schedule
- **Daily Backups**: Retained for 30 days
- **Weekly Backups**: Retained for 12 weeks (3 months)
- **Monthly Backups**: Retained for 12 months
- **Yearly Backups**: Retained for 7 years (HIPAA compliance)

#### Storage Locations
- **Primary**: Local backup server (RAID 10)
- **Secondary**: Cloud storage (AWS S3 Glacier)
- **Tertiary**: Offsite backup facility

## Disaster Recovery Plan

### 1. Recovery Time Objectives (RTO)

| Scenario | Target RTO | Description |
|----------|------------|-------------|
| Database Corruption | 2 hours | Restore from latest backup |
| Server Hardware Failure | 4 hours | Failover to backup server |
| Data Center Outage | 8 hours | Activate disaster recovery site |
| Complete Infrastructure Loss | 24 hours | Full rebuild from backups |

### 2. Recovery Point Objectives (RPO)

| Data Type | Target RPO | Backup Frequency |
|-----------|------------|------------------|
| Patient Data | 15 minutes | Continuous WAL |
| Application Data | 1 hour | Incremental backups |
| File Uploads | 4 hours | Scheduled sync |
| Configuration | 24 hours | Daily backups |

### 3. Disaster Recovery Procedures

#### Database Recovery
```bash
# 1. Stop application services
systemctl stop dentopro-app
systemctl stop nginx

# 2. Restore database from backup
pg_restore -h $DB_HOST -U $DB_USER -d dentopro_db \
  --clean --if-exists --verbose \
  /backups/daily/dentopro_20241221.backup

# 3. Apply WAL files for point-in-time recovery
pg_ctl start -D /var/lib/postgresql/data
psql -d dentopro_db -c "SELECT pg_start_backup('disaster_recovery');"

# 4. Verify data integrity
psql -d dentopro_db -c "
  SELECT 
    COUNT(*) as total_patients,
    COUNT(DISTINCT doctor_id) as total_doctors,
    MAX(created_at) as latest_record
  FROM patients;
"

# 5. Restart services
systemctl start dentopro-app
systemctl start nginx
```

#### File System Recovery
```bash
# 1. Mount backup storage
mount /dev/backup_disk /mnt/backup

# 2. Restore file uploads
rsync -avz /mnt/backup/files/uploads/ /app/storage/uploads/

# 3. Set proper permissions
chown -R www-data:www-data /app/storage/uploads/
chmod -R 755 /app/storage/uploads/

# 4. Verify file integrity
find /app/storage/uploads/ -type f -exec md5sum {} \; > /tmp/file_checksums.txt
```

#### Application Recovery
```bash
# 1. Clone application from backup
git clone /backups/code/dentopro_20241221.bundle /app/dentopro-recovery

# 2. Restore configuration
tar -xzf /backups/config/config_20241221.tar.gz -C /app/

# 3. Install dependencies
cd /app/dentopro-recovery
npm install --production

# 4. Build application
npm run build

# 5. Start services
docker-compose up -d
```

## Monitoring and Alerting

### 1. Backup Monitoring
```bash
# Check backup completion
#!/bin/bash
BACKUP_FILE="/backups/daily/dentopro_$(date +%Y%m%d).backup"
if [ ! -f "$BACKUP_FILE" ]; then
  echo "ALERT: Daily backup missing for $(date +%Y-%m-%d)" | \
    mail -s "Backup Failure Alert" <EMAIL>
fi

# Verify backup integrity
pg_restore --list $BACKUP_FILE > /dev/null 2>&1
if [ $? -ne 0 ]; then
  echo "ALERT: Backup file corrupted: $BACKUP_FILE" | \
    mail -s "Backup Corruption Alert" <EMAIL>
fi
```

### 2. System Health Monitoring
```sql
-- Database health check
SELECT 
  pg_database_size('dentopro_db') as db_size,
  pg_stat_get_db_numbackends(oid) as active_connections,
  pg_stat_get_db_xact_commit(oid) as transactions
FROM pg_database 
WHERE datname = 'dentopro_db';

-- Check for long-running queries
SELECT 
  pid,
  now() - pg_stat_activity.query_start AS duration,
  query 
FROM pg_stat_activity 
WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes';
```

### 3. Automated Recovery Testing
```bash
# Monthly disaster recovery test
#!/bin/bash
# 1. Create test environment
docker-compose -f docker-compose.test.yml up -d

# 2. Restore latest backup to test environment
pg_restore -h test-db -U postgres -d dentopro_test \
  /backups/daily/dentopro_$(date +%Y%m%d).backup

# 3. Run application tests
npm run test:integration

# 4. Generate recovery test report
echo "Recovery test completed: $(date)" >> /logs/recovery_tests.log

# 5. Cleanup test environment
docker-compose -f docker-compose.test.yml down
```

## Security Considerations

### 1. Backup Encryption
```bash
# Encrypt backups before storage
gpg --cipher-algo AES256 --compress-algo 1 --s2k-cipher-algo AES256 \
  --s2k-digest-algo SHA512 --s2k-mode 3 --s2k-count 65536 \
  --symmetric --output dentopro_encrypted.backup.gpg \
  dentopro_20241221.backup
```

### 2. Access Control
- Backup files encrypted with AES-256
- Access restricted to authorized personnel only
- Multi-factor authentication required for backup access
- Audit logs for all backup and recovery operations

### 3. Compliance Requirements

#### HIPAA Compliance
- Encrypted backups (at rest and in transit)
- Access logs and audit trails
- Business Associate Agreements with cloud providers
- Regular security assessments

#### GDPR Compliance
- Right to be forgotten implementation
- Data portability support
- Breach notification procedures
- Data retention policy enforcement

## Recovery Procedures by Scenario

### Scenario 1: Single Doctor Data Loss
```sql
-- Restore specific doctor's data
BEGIN;

-- Backup current state
CREATE TABLE patients_backup AS SELECT * FROM patients WHERE doctor_id = $1;

-- Restore from backup
COPY patients FROM '/backups/doctors/patients_$1.csv' WITH CSV HEADER;

-- Verify restoration
SELECT COUNT(*) FROM patients WHERE doctor_id = $1;

COMMIT;
```

### Scenario 2: Database Corruption
1. **Immediate Response**
   - Stop application to prevent further corruption
   - Assess extent of corruption
   - Notify stakeholders

2. **Recovery Steps**
   - Restore from latest clean backup
   - Apply WAL files for minimal data loss
   - Verify data integrity
   - Resume operations

3. **Post-Recovery**
   - Investigate root cause
   - Update procedures if necessary
   - Document lessons learned

### Scenario 3: Complete System Failure
1. **Emergency Response**
   - Activate disaster recovery team
   - Assess infrastructure damage
   - Communicate with stakeholders

2. **Recovery Process**
   - Deploy backup infrastructure
   - Restore all data and applications
   - Test system functionality
   - Redirect traffic to recovery site

3. **Return to Normal**
   - Rebuild primary infrastructure
   - Sync data from recovery site
   - Switch back to primary site
   - Conduct post-incident review

## Testing and Validation

### Monthly Tests
- Backup restoration verification
- Database integrity checks
- Application functionality tests
- Performance benchmarks

### Quarterly Tests
- Full disaster recovery simulation
- Cross-site failover testing
- Security penetration testing
- Compliance audit preparation

### Annual Tests
- Complete infrastructure rebuild
- Business continuity validation
- Stakeholder communication drills
- Recovery procedure updates

This comprehensive backup and disaster recovery plan ensures the DentoPro platform can maintain high availability and data integrity while meeting healthcare industry compliance requirements.
