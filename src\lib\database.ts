// Database configuration and connection management
import { Pool, PoolClient, QueryResult, QueryResultRow } from 'pg';

// Database configuration interface
interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl?: boolean;
  maxConnections?: number;
  idleTimeoutMillis?: number;
  connectionTimeoutMillis?: number;
}

// Query result interface
interface QueryOptions {
  text: string;
  values?: any[];
}

// Database connection pool
let pool: Pool | null = null;

// Initialize database connection pool
export function initializeDatabase(config?: DatabaseConfig): Pool {
  if (pool) {
    return pool;
  }

  const dbConfig: DatabaseConfig = config || {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    database: process.env.DB_NAME || 'dentopro_db',
    username: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || '',
    ssl: process.env.NODE_ENV === 'production',
    maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '20'),
    idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT || '30000'),
    connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT || '2000'),
  };

  pool = new Pool({
    host: dbConfig.host,
    port: dbConfig.port,
    database: dbConfig.database,
    user: dbConfig.username,
    password: dbConfig.password,
    ssl: dbConfig.ssl ? { rejectUnauthorized: false } : false,
    max: dbConfig.maxConnections,
    idleTimeoutMillis: dbConfig.idleTimeoutMillis,
    connectionTimeoutMillis: dbConfig.connectionTimeoutMillis,
  });

  // Handle pool errors
  pool.on('error', (err) => {
    console.error('Unexpected error on idle client', err);
    process.exit(-1);
  });

  return pool;
}

// Get database connection pool
export function getDatabase(): Pool {
  if (!pool) {
    return initializeDatabase();
  }
  return pool;
}

// Execute a query with automatic connection management
export async function query<T extends QueryResultRow = any>(
  text: string,
  values?: any[]
): Promise<QueryResult<T>> {
  const db = getDatabase();
  const start = Date.now();
  
  try {
    const result = await db.query<T>(text, values);
    const duration = Date.now() - start;
    
    // Log slow queries in development
    if (process.env.NODE_ENV === 'development' && duration > 1000) {
      console.warn(`Slow query detected (${duration}ms):`, text);
    }
    
    return result;
  } catch (error) {
    console.error('Database query error:', error);
    console.error('Query:', text);
    console.error('Values:', values);
    throw error;
  }
}

// Execute a query with a specific client (for transactions)
export async function queryWithClient<T extends QueryResultRow = any>(
  client: PoolClient,
  text: string,
  values?: any[]
): Promise<QueryResult<T>> {
  const start = Date.now();
  
  try {
    const result = await client.query<T>(text, values);
    const duration = Date.now() - start;
    
    // Log slow queries in development
    if (process.env.NODE_ENV === 'development' && duration > 1000) {
      console.warn(`Slow query detected (${duration}ms):`, text);
    }
    
    return result;
  } catch (error) {
    console.error('Database query error:', error);
    console.error('Query:', text);
    console.error('Values:', values);
    throw error;
  }
}

// Get a client for transactions
export async function getClient(): Promise<PoolClient> {
  const db = getDatabase();
  return await db.connect();
}

// Execute multiple queries in a transaction
export async function transaction<T>(
  callback: (client: PoolClient) => Promise<T>
): Promise<T> {
  const client = await getClient();
  
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

// Database health check
export async function healthCheck(): Promise<boolean> {
  try {
    const result = await query('SELECT 1 as health');
    return result.rows.length > 0 && result.rows[0].health === 1;
  } catch (error) {
    console.error('Database health check failed:', error);
    return false;
  }
}

// Close database connections
export async function closeDatabase(): Promise<void> {
  if (pool) {
    await pool.end();
    pool = null;
  }
}

// Database utility functions
export const db = {
  // Basic query execution
  query,
  
  // Transaction support
  transaction,
  getClient,
  queryWithClient,
  
  // Connection management
  initialize: initializeDatabase,
  close: closeDatabase,
  healthCheck,
  
  // Utility methods
  async findOne<T extends QueryResultRow = any>(table: string, conditions: Record<string, any>): Promise<T | null> {
    const keys = Object.keys(conditions);
    const values = Object.values(conditions);
    const whereClause = keys.map((key, index) => `${key} = $${index + 1}`).join(' AND ');
    
    const result = await query<T>(
      `SELECT * FROM ${table} WHERE ${whereClause} LIMIT 1`,
      values
    );
    
    return result.rows[0] || null;
  },
  
  async findMany<T extends QueryResultRow = any>(
    table: string, 
    conditions: Record<string, any> = {},
    options: { limit?: number; offset?: number; orderBy?: string } = {}
  ): Promise<T[]> {
    const keys = Object.keys(conditions);
    const values = Object.values(conditions);
    
    let query_text = `SELECT * FROM ${table}`;
    
    if (keys.length > 0) {
      const whereClause = keys.map((key, index) => `${key} = $${index + 1}`).join(' AND ');
      query_text += ` WHERE ${whereClause}`;
    }
    
    if (options.orderBy) {
      query_text += ` ORDER BY ${options.orderBy}`;
    }
    
    if (options.limit) {
      query_text += ` LIMIT ${options.limit}`;
    }
    
    if (options.offset) {
      query_text += ` OFFSET ${options.offset}`;
    }
    
    const result = await query<T>(query_text, values);
    return result.rows;
  },
  
  async insert<T extends QueryResultRow = any>(
    table: string, 
    data: Record<string, any>
  ): Promise<T> {
    const keys = Object.keys(data);
    const values = Object.values(data);
    const placeholders = keys.map((_, index) => `$${index + 1}`).join(', ');
    const columns = keys.join(', ');
    
    const result = await query<T>(
      `INSERT INTO ${table} (${columns}) VALUES (${placeholders}) RETURNING *`,
      values
    );
    
    return result.rows[0];
  },
  
  async update<T extends QueryResultRow = any>(
    table: string,
    data: Record<string, any>,
    conditions: Record<string, any>
  ): Promise<T | null> {
    const dataKeys = Object.keys(data);
    const dataValues = Object.values(data);
    const conditionKeys = Object.keys(conditions);
    const conditionValues = Object.values(conditions);
    
    const setClause = dataKeys.map((key, index) => `${key} = $${index + 1}`).join(', ');
    const whereClause = conditionKeys.map(
      (key, index) => `${key} = $${dataKeys.length + index + 1}`
    ).join(' AND ');
    
    const result = await query<T>(
      `UPDATE ${table} SET ${setClause} WHERE ${whereClause} RETURNING *`,
      [...dataValues, ...conditionValues]
    );
    
    return result.rows[0] || null;
  },
  
  async delete(
    table: string,
    conditions: Record<string, any>
  ): Promise<number> {
    const keys = Object.keys(conditions);
    const values = Object.values(conditions);
    const whereClause = keys.map((key, index) => `${key} = $${index + 1}`).join(' AND ');
    
    const result = await query(
      `DELETE FROM ${table} WHERE ${whereClause}`,
      values
    );
    
    return result.rowCount || 0;
  },
  
  async count(
    table: string,
    conditions: Record<string, any> = {}
  ): Promise<number> {
    const keys = Object.keys(conditions);
    const values = Object.values(conditions);
    
    let query_text = `SELECT COUNT(*) as count FROM ${table}`;
    
    if (keys.length > 0) {
      const whereClause = keys.map((key, index) => `${key} = $${index + 1}`).join(' AND ');
      query_text += ` WHERE ${whereClause}`;
    }
    
    const result = await query<{ count: string }>(query_text, values);
    return parseInt(result.rows[0].count);
  }
};

// Export default database instance
export default db;
