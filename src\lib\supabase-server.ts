// Server-side Supabase utilities
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from './supabase';

// Server component client (for Next.js server components)
export const createServerSupabaseClient = async () => {
  const cookieStore = await cookies();
  
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet: any[]) {
          cookiesToSet.forEach(({ name, value, options }: any) => {
            cookieStore.set(name, value, options);
          });
        },
      },
    }
  );
};

export default createServerSupabaseClient;
