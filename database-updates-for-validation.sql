-- =============================================
-- DENTOPRO SAAS - DATABASE UPDATES FOR VALIDATION & PUBLIC BOOKING
-- =============================================
-- Run this script in your Supabase SQL Editor to add missing features
-- This adds validation and public booking functionality to your existing database

-- =============================================
-- 1. ADD CLINIC_SLUG COLUMN TO DOCTORS TABLE
-- =============================================

-- Add clinic_slug column for unique booking URLs
ALTER TABLE doctors ADD COLUMN IF NOT EXISTS clinic_slug VARCHAR(255);

-- Create unique constraint on clinic_slug
ALTER TABLE doctors ADD CONSTRAINT unique_clinic_slug UNIQUE (clinic_slug);

-- Create index for fast slug lookups
CREATE INDEX IF NOT EXISTS idx_doctors_clinic_slug ON doctors(clinic_slug);

-- =============================================
-- 2. UNIQUE SLUG GENERATION FUNCTION
-- =============================================

-- Function to generate unique clinic slug
CREATE OR REPLACE FUNCTION generate_unique_clinic_slug(clinic_name_input VARCHAR(255), doctor_id_input UUID DEFAULT NULL)
RETURNS VARCHAR(255) AS $$
DECLARE
    base_slug VARCHAR(255);
    final_slug VARCHAR(255);
    counter INTEGER := 0;
    slug_exists BOOLEAN;
BEGIN
    -- Generate base slug from clinic name
    base_slug := LOWER(TRIM(clinic_name_input));
    base_slug := REGEXP_REPLACE(base_slug, '[^a-z0-9\s]', '', 'g');
    base_slug := REGEXP_REPLACE(base_slug, '\s+', '-', 'g');
    base_slug := TRIM(base_slug, '-');
    
    -- Ensure minimum length
    IF LENGTH(base_slug) < 3 THEN
        base_slug := 'clinic-' || base_slug;
    END IF;
    
    -- Check if base slug is available
    final_slug := base_slug;
    
    LOOP
        -- Check if slug exists (excluding current doctor if updating)
        SELECT EXISTS(
            SELECT 1 FROM doctors 
            WHERE clinic_slug = final_slug 
            AND (doctor_id_input IS NULL OR id != doctor_id_input)
        ) INTO slug_exists;
        
        -- If slug doesn't exist, we can use it
        IF NOT slug_exists THEN
            EXIT;
        END IF;
        
        -- If slug exists, add counter
        counter := counter + 1;
        final_slug := base_slug || '-' || counter;
        
        -- Safety check to prevent infinite loop
        IF counter > 9999 THEN
            final_slug := base_slug || '-' || EXTRACT(EPOCH FROM NOW())::INTEGER;
            EXIT;
        END IF;
    END LOOP;
    
    RETURN final_slug;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- 3. AUTO-GENERATE SLUGS FOR EXISTING DOCTORS
-- =============================================

-- Update existing doctors with unique slugs
UPDATE doctors 
SET clinic_slug = generate_unique_clinic_slug(clinic_name, id)
WHERE clinic_slug IS NULL;

-- Make clinic_slug NOT NULL after populating existing records
ALTER TABLE doctors ALTER COLUMN clinic_slug SET NOT NULL;

-- =============================================
-- 4. AUTOMATIC SLUG GENERATION TRIGGERS
-- =============================================

-- Function for trigger to auto-generate slug
CREATE OR REPLACE FUNCTION auto_generate_clinic_slug()
RETURNS TRIGGER AS $$
BEGIN
    -- Only generate slug if not provided
    IF NEW.clinic_slug IS NULL OR NEW.clinic_slug = '' THEN
        NEW.clinic_slug := generate_unique_clinic_slug(NEW.clinic_name, NEW.id);
    ELSE
        -- Validate provided slug is unique
        IF EXISTS(SELECT 1 FROM doctors WHERE clinic_slug = NEW.clinic_slug AND id != NEW.id) THEN
            RAISE EXCEPTION 'Clinic slug "%" already exists', NEW.clinic_slug;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for INSERT
DROP TRIGGER IF EXISTS trigger_auto_generate_clinic_slug_insert ON doctors;
CREATE TRIGGER trigger_auto_generate_clinic_slug_insert
    BEFORE INSERT ON doctors
    FOR EACH ROW
    EXECUTE FUNCTION auto_generate_clinic_slug();

-- Create trigger for UPDATE (when clinic_name changes)
DROP TRIGGER IF EXISTS trigger_auto_generate_clinic_slug_update ON doctors;
CREATE TRIGGER trigger_auto_generate_clinic_slug_update
    BEFORE UPDATE ON doctors
    FOR EACH ROW
    WHEN (OLD.clinic_name IS DISTINCT FROM NEW.clinic_name AND (NEW.clinic_slug IS NULL OR NEW.clinic_slug = ''))
    EXECUTE FUNCTION auto_generate_clinic_slug();

-- =============================================
-- 5. ENHANCE CLINIC_SETTINGS FOR PUBLIC BOOKING
-- =============================================

-- Add missing columns to clinic_settings if they don't exist
ALTER TABLE clinic_settings ADD COLUMN IF NOT EXISTS clinic_name VARCHAR(255);
ALTER TABLE clinic_settings ADD COLUMN IF NOT EXISTS phone VARCHAR(20);
ALTER TABLE clinic_settings ADD COLUMN IF NOT EXISTS whatsapp VARCHAR(20);
ALTER TABLE clinic_settings ADD COLUMN IF NOT EXISTS buffer_time INTEGER DEFAULT 15;
ALTER TABLE clinic_settings ADD COLUMN IF NOT EXISTS booking_advance_days INTEGER DEFAULT 30;
ALTER TABLE clinic_settings ADD COLUMN IF NOT EXISTS booking_notice_hours INTEGER DEFAULT 2;

-- =============================================
-- 6. VALIDATION HELPER FUNCTIONS
-- =============================================

-- Function to check for duplicate emails
CREATE OR REPLACE FUNCTION check_duplicate_email(email_input VARCHAR(255), exclude_doctor_id UUID DEFAULT NULL)
RETURNS TABLE(
    exists_flag BOOLEAN,
    doctor_name VARCHAR(255),
    clinic_name VARCHAR(255)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        EXISTS(
            SELECT 1 FROM doctors 
            WHERE email = email_input 
            AND (exclude_doctor_id IS NULL OR id != exclude_doctor_id)
        ) as exists_flag,
        d.full_name as doctor_name,
        d.clinic_name as clinic_name
    FROM doctors d
    WHERE d.email = email_input 
    AND (exclude_doctor_id IS NULL OR d.id != exclude_doctor_id)
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Function to check for duplicate license numbers
CREATE OR REPLACE FUNCTION check_duplicate_license(license_input VARCHAR(100), exclude_doctor_id UUID DEFAULT NULL)
RETURNS TABLE(
    exists_flag BOOLEAN,
    doctor_name VARCHAR(255),
    clinic_name VARCHAR(255)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        EXISTS(
            SELECT 1 FROM doctors 
            WHERE license_number = license_input 
            AND (exclude_doctor_id IS NULL OR id != exclude_doctor_id)
        ) as exists_flag,
        d.full_name as doctor_name,
        d.clinic_name as clinic_name
    FROM doctors d
    WHERE d.license_number = license_input 
    AND (exclude_doctor_id IS NULL OR d.id != exclude_doctor_id)
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Function to find similar doctor names
CREATE OR REPLACE FUNCTION find_similar_doctors(name_input VARCHAR(255), exclude_doctor_id UUID DEFAULT NULL)
RETURNS TABLE(
    doctor_name VARCHAR(255),
    clinic_name VARCHAR(255),
    email VARCHAR(255)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        d.full_name as doctor_name,
        d.clinic_name as clinic_name,
        d.email as email
    FROM doctors d
    WHERE d.full_name ILIKE '%' || name_input || '%'
    AND (exclude_doctor_id IS NULL OR d.id != exclude_doctor_id)
    ORDER BY d.full_name
    LIMIT 5;
END;
$$ LANGUAGE plpgsql;

-- Function to find similar clinic names
CREATE OR REPLACE FUNCTION find_similar_clinics(clinic_input VARCHAR(255), exclude_doctor_id UUID DEFAULT NULL)
RETURNS TABLE(
    clinic_name VARCHAR(255),
    doctor_name VARCHAR(255),
    clinic_slug VARCHAR(255)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        d.clinic_name as clinic_name,
        d.full_name as doctor_name,
        d.clinic_slug as clinic_slug
    FROM doctors d
    WHERE d.clinic_name ILIKE '%' || clinic_input || '%'
    AND (exclude_doctor_id IS NULL OR d.id != exclude_doctor_id)
    ORDER BY d.clinic_name
    LIMIT 5;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- 7. PUBLIC BOOKING ENHANCEMENTS
-- =============================================

-- Add treatment_type column to appointments if missing
ALTER TABLE appointments ADD COLUMN IF NOT EXISTS treatment_type VARCHAR(100);

-- Update appointment status constraint to include new statuses
ALTER TABLE appointments DROP CONSTRAINT IF EXISTS chk_appointment_status;
ALTER TABLE appointments ADD CONSTRAINT chk_appointment_status 
    CHECK (status IN ('scheduled', 'confirmed', 'pending', 'completed', 'cancelled', 'no_show'));

-- =============================================
-- 8. COMMENTS FOR DOCUMENTATION
-- =============================================

COMMENT ON COLUMN doctors.clinic_slug IS 'Unique URL slug for public booking page (e.g., "hassan-dental-clinic")';
COMMENT ON FUNCTION generate_unique_clinic_slug IS 'Generates unique clinic slug with collision detection';
COMMENT ON FUNCTION auto_generate_clinic_slug IS 'Trigger function to auto-generate clinic slugs';

-- =============================================
-- 9. VERIFICATION QUERY
-- =============================================

-- Verify the updates were applied successfully
SELECT 
    'Database updates completed successfully!' as status,
    COUNT(*) as total_doctors,
    COUNT(clinic_slug) as doctors_with_slugs,
    COUNT(DISTINCT clinic_slug) as unique_slugs
FROM doctors;

-- Show sample of generated slugs
SELECT 
    full_name,
    clinic_name,
    clinic_slug,
    'http://localhost:3000/book/' || clinic_slug as booking_url
FROM doctors 
WHERE clinic_slug IS NOT NULL
LIMIT 5;
