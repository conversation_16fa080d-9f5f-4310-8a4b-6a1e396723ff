# ⚡ Database Performance Optimization

## Overview
This document outlines comprehensive performance optimization strategies for the DentoPro SaaS platform database, ensuring optimal performance as the platform scales to thousands of doctors and millions of patient records.

## Performance Optimization Strategy

### 1. Indexing Strategy

#### Primary Indexes (Already Implemented)
```sql
-- Doctor-based isolation indexes (CRITICAL)
CREATE INDEX idx_patients_doctor_id ON patients(doctor_id);
CREATE INDEX idx_appointments_doctor_id ON appointments(doctor_id);
CREATE INDEX idx_treatments_doctor_id ON treatments(doctor_id);
CREATE INDEX idx_materials_doctor_id ON materials(doctor_id);
CREATE INDEX idx_invoices_doctor_id ON invoices(doctor_id);

-- Composite indexes for common queries
CREATE INDEX idx_appointments_doctor_date ON appointments(doctor_id, appointment_date);
CREATE INDEX idx_treatments_patient_date ON treatments(patient_id, created_at);
CREATE INDEX idx_materials_low_stock ON materials(doctor_id, current_stock, low_threshold);
```

#### Advanced Indexing Strategies
```sql
-- Partial indexes for active records only
CREATE INDEX idx_active_patients ON patients(doctor_id, full_name) 
WHERE is_active = true;

CREATE INDEX idx_pending_appointments ON appointments(doctor_id, appointment_date) 
WHERE status IN ('scheduled', 'confirmed');

-- Functional indexes for search optimization
CREATE INDEX idx_patients_name_search ON patients 
USING gin(to_tsvector('english', full_name));

CREATE INDEX idx_materials_name_search ON materials 
USING gin(to_tsvector('english', name));

-- JSONB indexes for metadata queries
CREATE INDEX idx_patient_medical_history ON patients 
USING gin(medical_history);

CREATE INDEX idx_treatment_materials ON treatments 
USING gin(materials_used);
```

### 2. Query Optimization

#### Optimized Query Patterns
```sql
-- Efficient patient lookup with pagination
SELECT p.*, 
       COUNT(*) OVER() as total_count
FROM patients p
WHERE p.doctor_id = $1
  AND ($2 IS NULL OR p.full_name ILIKE '%' || $2 || '%')
ORDER BY p.created_at DESC
LIMIT $3 OFFSET $4;

-- Appointment calendar view with patient info
SELECT a.id, a.appointment_date, a.appointment_time, a.duration,
       a.appointment_type, a.status,
       p.full_name as patient_name, p.phone as patient_phone
FROM appointments a
JOIN patients p ON a.patient_id = p.id
WHERE a.doctor_id = $1
  AND a.appointment_date BETWEEN $2 AND $3
ORDER BY a.appointment_date, a.appointment_time;

-- Dashboard statistics (optimized)
WITH stats AS (
  SELECT 
    COUNT(CASE WHEN DATE(created_at) = CURRENT_DATE THEN 1 END) as today_patients,
    COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as month_patients,
    COUNT(*) as total_patients
  FROM patients 
  WHERE doctor_id = $1
),
appointment_stats AS (
  SELECT 
    COUNT(CASE WHEN appointment_date = CURRENT_DATE THEN 1 END) as today_appointments,
    COUNT(CASE WHEN appointment_date >= CURRENT_DATE AND appointment_date <= CURRENT_DATE + INTERVAL '7 days' THEN 1 END) as week_appointments
  FROM appointments 
  WHERE doctor_id = $1
)
SELECT * FROM stats, appointment_stats;
```

#### Query Performance Guidelines
1. **Always filter by doctor_id first** - Ensures data isolation and uses primary indexes
2. **Use LIMIT and OFFSET** - Implement pagination for large result sets
3. **Avoid SELECT \*** - Only select needed columns
4. **Use prepared statements** - Reduce query parsing overhead
5. **Batch operations** - Group multiple inserts/updates when possible

### 3. Connection Pooling

#### Optimal Pool Configuration
```typescript
// Production connection pool settings
const poolConfig = {
  max: 20,                    // Maximum connections
  min: 5,                     // Minimum connections
  idleTimeoutMillis: 30000,   // Close idle connections after 30s
  connectionTimeoutMillis: 2000, // Timeout for new connections
  maxUses: 7500,              // Rotate connections after 7500 uses
  allowExitOnIdle: true       // Allow process to exit when idle
};
```

#### Connection Pool Monitoring
```sql
-- Monitor active connections
SELECT 
  state,
  COUNT(*) as connection_count,
  AVG(EXTRACT(EPOCH FROM (now() - state_change))) as avg_duration
FROM pg_stat_activity 
WHERE datname = 'dentopro_db'
GROUP BY state;

-- Check for connection leaks
SELECT 
  client_addr,
  COUNT(*) as connections,
  MAX(backend_start) as oldest_connection
FROM pg_stat_activity 
WHERE datname = 'dentopro_db'
GROUP BY client_addr
HAVING COUNT(*) > 5;
```

### 4. Caching Strategy

#### Application-Level Caching
```typescript
// Redis caching for frequently accessed data
import Redis from 'ioredis';

const redis = new Redis({
  host: process.env.REDIS_HOST,
  port: parseInt(process.env.REDIS_PORT || '6379'),
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3
});

// Cache doctor settings
export async function getDoctorSettings(doctorId: string) {
  const cacheKey = `doctor:${doctorId}:settings`;
  
  // Try cache first
  const cached = await redis.get(cacheKey);
  if (cached) {
    return JSON.parse(cached);
  }
  
  // Fetch from database
  const settings = await db.findOne('clinic_settings', { doctor_id: doctorId });
  
  // Cache for 1 hour
  await redis.setex(cacheKey, 3600, JSON.stringify(settings));
  
  return settings;
}

// Cache patient summary data
export async function getPatientSummary(patientId: string) {
  const cacheKey = `patient:${patientId}:summary`;
  
  const cached = await redis.get(cacheKey);
  if (cached) {
    return JSON.parse(cached);
  }
  
  const summary = await db.query(`
    SELECT 
      p.*,
      COUNT(a.id) as total_appointments,
      MAX(a.appointment_date) as last_visit,
      SUM(i.total_amount) as total_billed
    FROM patients p
    LEFT JOIN appointments a ON p.id = a.patient_id
    LEFT JOIN invoices i ON p.id = i.patient_id
    WHERE p.id = $1
    GROUP BY p.id
  `, [patientId]);
  
  // Cache for 30 minutes
  await redis.setex(cacheKey, 1800, JSON.stringify(summary.rows[0]));
  
  return summary.rows[0];
}
```

#### Database-Level Caching
```sql
-- Materialized views for complex aggregations
CREATE MATERIALIZED VIEW doctor_dashboard_stats AS
SELECT 
  d.id as doctor_id,
  d.clinic_name,
  COUNT(DISTINCT p.id) as total_patients,
  COUNT(DISTINCT a.id) as total_appointments,
  COUNT(DISTINCT CASE WHEN a.appointment_date >= CURRENT_DATE - INTERVAL '30 days' THEN a.id END) as recent_appointments,
  SUM(i.total_amount) as total_revenue,
  AVG(i.total_amount) as avg_invoice_amount
FROM doctors d
LEFT JOIN patients p ON d.id = p.doctor_id
LEFT JOIN appointments a ON d.id = a.doctor_id
LEFT JOIN invoices i ON d.id = i.doctor_id
GROUP BY d.id, d.clinic_name;

-- Refresh materialized view hourly
CREATE OR REPLACE FUNCTION refresh_dashboard_stats()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY doctor_dashboard_stats;
END;
$$ LANGUAGE plpgsql;

-- Schedule refresh
SELECT cron.schedule('refresh-dashboard', '0 * * * *', 'SELECT refresh_dashboard_stats();');
```

### 5. Partitioning Strategy

#### Time-Based Partitioning for Large Tables
```sql
-- Partition system_logs by month
CREATE TABLE system_logs_partitioned (
  LIKE system_logs INCLUDING ALL
) PARTITION BY RANGE (created_at);

-- Create monthly partitions
CREATE TABLE system_logs_2024_01 PARTITION OF system_logs_partitioned
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

CREATE TABLE system_logs_2024_02 PARTITION OF system_logs_partitioned
FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');

-- Auto-create future partitions
CREATE OR REPLACE FUNCTION create_monthly_partition(table_name text, start_date date)
RETURNS void AS $$
DECLARE
  partition_name text;
  end_date date;
BEGIN
  partition_name := table_name || '_' || to_char(start_date, 'YYYY_MM');
  end_date := start_date + interval '1 month';
  
  EXECUTE format('CREATE TABLE %I PARTITION OF %I FOR VALUES FROM (%L) TO (%L)',
    partition_name, table_name, start_date, end_date);
END;
$$ LANGUAGE plpgsql;
```

#### Doctor-Based Partitioning for Extreme Scale
```sql
-- Partition patients table by doctor_id hash (for 10,000+ doctors)
CREATE TABLE patients_partitioned (
  LIKE patients INCLUDING ALL
) PARTITION BY HASH (doctor_id);

-- Create 16 hash partitions
DO $$
BEGIN
  FOR i IN 0..15 LOOP
    EXECUTE format('CREATE TABLE patients_part_%s PARTITION OF patients_partitioned FOR VALUES WITH (modulus 16, remainder %s)', i, i);
  END LOOP;
END $$;
```

### 6. Monitoring and Alerting

#### Performance Monitoring Queries
```sql
-- Slow query monitoring
SELECT 
  query,
  calls,
  total_time,
  mean_time,
  rows,
  100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- Index usage analysis
SELECT 
  schemaname,
  tablename,
  indexname,
  idx_tup_read,
  idx_tup_fetch,
  idx_scan
FROM pg_stat_user_indexes 
ORDER BY idx_scan DESC;

-- Table size monitoring
SELECT 
  schemaname,
  tablename,
  pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
  pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY size_bytes DESC;
```

#### Automated Performance Alerts
```sql
-- Function to check for performance issues
CREATE OR REPLACE FUNCTION check_performance_issues()
RETURNS TABLE(issue_type text, description text, severity text) AS $$
BEGIN
  -- Check for slow queries
  RETURN QUERY
  SELECT 
    'slow_query'::text,
    'Query: ' || substr(query, 1, 100) || '... (avg: ' || round(mean_time::numeric, 2) || 'ms)'::text,
    CASE 
      WHEN mean_time > 5000 THEN 'critical'
      WHEN mean_time > 1000 THEN 'warning'
      ELSE 'info'
    END::text
  FROM pg_stat_statements 
  WHERE mean_time > 500
  ORDER BY mean_time DESC
  LIMIT 5;
  
  -- Check for unused indexes
  RETURN QUERY
  SELECT 
    'unused_index'::text,
    'Index: ' || indexname || ' on table ' || tablename::text,
    'warning'::text
  FROM pg_stat_user_indexes 
  WHERE idx_scan = 0 
  AND schemaname = 'public';
  
  -- Check for table bloat
  RETURN QUERY
  SELECT 
    'table_bloat'::text,
    'Table: ' || tablename || ' size: ' || pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename))::text,
    CASE 
      WHEN pg_total_relation_size(schemaname||'.'||tablename) > 1073741824 THEN 'warning' -- 1GB
      ELSE 'info'
    END::text
  FROM pg_tables 
  WHERE schemaname = 'public'
  AND pg_total_relation_size(schemaname||'.'||tablename) > 104857600; -- 100MB
END;
$$ LANGUAGE plpgsql;
```

### 7. Optimization Maintenance

#### Regular Maintenance Tasks
```sql
-- Automated VACUUM and ANALYZE
CREATE OR REPLACE FUNCTION maintenance_vacuum_analyze()
RETURNS void AS $$
DECLARE
  table_record RECORD;
BEGIN
  FOR table_record IN 
    SELECT schemaname, tablename 
    FROM pg_tables 
    WHERE schemaname = 'public'
  LOOP
    EXECUTE 'VACUUM ANALYZE ' || quote_ident(table_record.schemaname) || '.' || quote_ident(table_record.tablename);
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Schedule maintenance (daily at 3 AM)
SELECT cron.schedule('vacuum-analyze', '0 3 * * *', 'SELECT maintenance_vacuum_analyze();');

-- Update table statistics
CREATE OR REPLACE FUNCTION update_table_statistics()
RETURNS void AS $$
BEGIN
  ANALYZE;
  
  -- Update custom statistics
  INSERT INTO platform_analytics (metric_type, metric_value, recorded_at)
  VALUES 
    ('database_size', pg_database_size('dentopro_db'), CURRENT_TIMESTAMP),
    ('total_connections', (SELECT count(*) FROM pg_stat_activity), CURRENT_TIMESTAMP),
    ('cache_hit_ratio', (SELECT round(100.0 * sum(blks_hit) / sum(blks_hit + blks_read), 2) FROM pg_stat_database), CURRENT_TIMESTAMP);
END;
$$ LANGUAGE plpgsql;
```

### 8. Performance Testing Framework

#### Load Testing Scenarios
```typescript
// Performance test for patient search
export async function testPatientSearch(doctorId: string, iterations: number = 1000) {
  const startTime = Date.now();
  const results = [];
  
  for (let i = 0; i < iterations; i++) {
    const searchTerm = generateRandomName();
    const queryStart = Date.now();
    
    await db.query(`
      SELECT id, full_name, phone, email 
      FROM patients 
      WHERE doctor_id = $1 
      AND full_name ILIKE $2 
      LIMIT 20
    `, [doctorId, `%${searchTerm}%`]);
    
    results.push(Date.now() - queryStart);
  }
  
  const totalTime = Date.now() - startTime;
  const avgTime = results.reduce((a, b) => a + b, 0) / results.length;
  const maxTime = Math.max(...results);
  const minTime = Math.min(...results);
  
  return {
    totalTime,
    avgTime,
    maxTime,
    minTime,
    throughput: iterations / (totalTime / 1000)
  };
}

// Concurrent user simulation
export async function simulateConcurrentUsers(userCount: number, duration: number) {
  const promises = [];
  
  for (let i = 0; i < userCount; i++) {
    promises.push(simulateUserSession(duration));
  }
  
  const results = await Promise.all(promises);
  
  return {
    totalQueries: results.reduce((sum, r) => sum + r.queryCount, 0),
    avgResponseTime: results.reduce((sum, r) => sum + r.avgResponseTime, 0) / results.length,
    errorRate: results.reduce((sum, r) => sum + r.errorCount, 0) / results.reduce((sum, r) => sum + r.queryCount, 0)
  };
}
```

This comprehensive performance optimization strategy ensures the DentoPro platform can scale efficiently while maintaining fast response times and optimal user experience.
