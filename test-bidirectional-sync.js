// =============================================
// DENTOPRO SAAS - BIDIRECTIONAL SYNC TESTER
// =============================================
// Run this in browser console to test real-time sync

console.log('🚀 DENTOPRO BIDIRECTIONAL SYNC TESTER');
console.log('=====================================');

// Test configuration
const TEST_CONFIG = {
  testDoctorEmail: '<EMAIL>',
  testPatientName: 'Sync Test Patient',
  testClinicName: 'Sync Test Clinic',
  supabaseUrl: 'https://ixqjqjqjqjqjqjqj.supabase.co',
  supabaseKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************.YJhJGJGJGJGJGJGJGJGJGJGJGJGJGJGJGJGJGJGJGJG'
};

// Initialize Supabase client for testing
const { createClient } = supabase;
const testClient = createClient(TEST_CONFIG.supabaseUrl, TEST_CONFIG.supabaseKey);

// Test results storage
let testResults = {
  appToSupabase: [],
  supabaseToApp: [],
  realTimeStatus: {},
  syncTimes: {}
};

// =============================================
// REAL-TIME CONNECTION TESTER
// =============================================

async function testRealTimeConnection() {
  console.log('\n🔄 TESTING REAL-TIME CONNECTION...');
  
  return new Promise((resolve) => {
    const testChannel = testClient
      .channel('connection-test')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'doctors' }, 
        (payload) => {
          console.log('✅ Real-time connection ACTIVE');
          testResults.realTimeStatus.connected = true;
          testResults.realTimeStatus.lastEvent = new Date();
          resolve(true);
        }
      )
      .subscribe((status) => {
        console.log('📡 Subscription status:', status);
        testResults.realTimeStatus.subscriptionStatus = status;
        
        if (status === 'SUBSCRIBED') {
          console.log('✅ Successfully subscribed to real-time updates');
          setTimeout(() => resolve(true), 1000); // Give it a moment
        } else if (status === 'CHANNEL_ERROR') {
          console.log('❌ Real-time subscription failed');
          testResults.realTimeStatus.connected = false;
          resolve(false);
        }
      });
  });
}

// =============================================
// BIDIRECTIONAL SYNC TESTS
// =============================================

async function testAppToSupabaseSync() {
  console.log('\n📤 TESTING: Application → Supabase Dashboard');
  console.log('===========================================');
  
  const startTime = Date.now();
  
  try {
    // Test 1: Insert new doctor via application API
    console.log('1️⃣ Creating doctor via application...');
    
    const testDoctor = {
      email: `test.${Date.now()}@sync.com`,
      full_name: 'Dr. Sync Test',
      clinic_name: 'Sync Test Clinic',
      license_number: `SYNC-${Date.now()}`,
      phone: '+**********',
      is_active: true,
      subscription_status: 'trial'
    };
    
    const { data: insertedDoctor, error: insertError } = await testClient
      .from('doctors')
      .insert(testDoctor)
      .select()
      .single();
    
    if (insertError) {
      console.log('❌ Insert failed:', insertError);
      testResults.appToSupabase.push({
        test: 'Doctor Insert',
        status: 'FAILED',
        error: insertError.message
      });
      return false;
    }
    
    console.log('✅ Doctor created:', insertedDoctor.full_name);
    
    // Test 2: Verify it appears in Supabase Dashboard
    console.log('2️⃣ Checking if data appears in Supabase Dashboard...');
    console.log('   👀 Go to Supabase Dashboard → Table Editor → doctors');
    console.log(`   🔍 Look for: ${testDoctor.full_name} (${testDoctor.email})`);
    
    const syncTime = Date.now() - startTime;
    testResults.syncTimes.appToSupabase = syncTime;
    
    testResults.appToSupabase.push({
      test: 'Doctor Insert',
      status: 'SUCCESS',
      syncTime: syncTime,
      data: insertedDoctor
    });
    
    console.log(`⏱️  Sync time: ${syncTime}ms`);
    return insertedDoctor;
    
  } catch (error) {
    console.log('❌ Test failed:', error);
    testResults.appToSupabase.push({
      test: 'Doctor Insert',
      status: 'ERROR',
      error: error.message
    });
    return false;
  }
}

async function testSupabaseToAppSync(doctorId) {
  console.log('\n📥 TESTING: Supabase Dashboard → Application');
  console.log('==========================================');
  
  if (!doctorId) {
    console.log('❌ No doctor ID provided for update test');
    return false;
  }
  
  const startTime = Date.now();
  
  try {
    // Set up real-time listener for updates
    let updateDetected = false;
    let updateData = null;
    
    const updateChannel = testClient
      .channel('update-test')
      .on('postgres_changes', 
        { 
          event: 'UPDATE', 
          schema: 'public', 
          table: 'doctors',
          filter: `id=eq.${doctorId}`
        }, 
        (payload) => {
          console.log('🔄 REAL-TIME UPDATE DETECTED!');
          console.log('   Data:', payload.new);
          updateDetected = true;
          updateData = payload.new;
          
          const syncTime = Date.now() - startTime;
          testResults.syncTimes.supabaseToApp = syncTime;
          console.log(`⏱️  Sync time: ${syncTime}ms`);
        }
      )
      .subscribe();
    
    // Wait for subscription to be ready
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('1️⃣ Real-time listener set up');
    console.log('2️⃣ Now go to Supabase Dashboard and update the doctor:');
    console.log(`   📝 Table Editor → doctors → Find ID: ${doctorId}`);
    console.log('   ✏️  Change clinic_name to: "Updated Sync Clinic"');
    console.log('   💾 Save the changes');
    console.log('   ⏳ Waiting for real-time update...');
    
    // Wait for update (30 second timeout)
    const timeout = 30000;
    const checkInterval = 500;
    let elapsed = 0;
    
    while (!updateDetected && elapsed < timeout) {
      await new Promise(resolve => setTimeout(resolve, checkInterval));
      elapsed += checkInterval;
      
      if (elapsed % 5000 === 0) {
        console.log(`   ⏳ Still waiting... (${elapsed/1000}s)`);
      }
    }
    
    // Cleanup
    testClient.removeChannel(updateChannel);
    
    if (updateDetected) {
      console.log('✅ BIDIRECTIONAL SYNC WORKING!');
      testResults.supabaseToApp.push({
        test: 'Doctor Update',
        status: 'SUCCESS',
        syncTime: testResults.syncTimes.supabaseToApp,
        data: updateData
      });
      return true;
    } else {
      console.log('❌ No real-time update detected within 30 seconds');
      console.log('   🔧 Check if real-time is enabled in Supabase');
      testResults.supabaseToApp.push({
        test: 'Doctor Update',
        status: 'TIMEOUT',
        message: 'No update detected within 30 seconds'
      });
      return false;
    }
    
  } catch (error) {
    console.log('❌ Test failed:', error);
    testResults.supabaseToApp.push({
      test: 'Doctor Update',
      status: 'ERROR',
      error: error.message
    });
    return false;
  }
}

// =============================================
// COMPREHENSIVE SYNC TEST
// =============================================

async function runComprehensiveSyncTest() {
  console.log('\n🎯 RUNNING COMPREHENSIVE BIDIRECTIONAL SYNC TEST');
  console.log('================================================');
  
  // Test 1: Real-time connection
  const connectionOk = await testRealTimeConnection();
  if (!connectionOk) {
    console.log('❌ Real-time connection failed. Aborting tests.');
    return;
  }
  
  // Test 2: App → Supabase
  const doctor = await testAppToSupabaseSync();
  if (!doctor) {
    console.log('❌ App → Supabase test failed. Skipping reverse test.');
    return;
  }
  
  // Test 3: Supabase → App
  await testSupabaseToAppSync(doctor.id);
  
  // Test Results Summary
  console.log('\n📊 TEST RESULTS SUMMARY');
  console.log('=======================');
  console.log('Real-time Status:', testResults.realTimeStatus);
  console.log('App → Supabase:', testResults.appToSupabase);
  console.log('Supabase → App:', testResults.supabaseToApp);
  console.log('Sync Times:', testResults.syncTimes);
  
  // Performance Analysis
  const appToSupabaseTime = testResults.syncTimes.appToSupabase;
  const supabaseToAppTime = testResults.syncTimes.supabaseToApp;
  
  console.log('\n⚡ PERFORMANCE ANALYSIS');
  console.log('======================');
  
  if (appToSupabaseTime) {
    if (appToSupabaseTime < 1000) {
      console.log(`✅ App → Supabase: ${appToSupabaseTime}ms (EXCELLENT)`);
    } else if (appToSupabaseTime < 3000) {
      console.log(`⚠️  App → Supabase: ${appToSupabaseTime}ms (ACCEPTABLE)`);
    } else {
      console.log(`❌ App → Supabase: ${appToSupabaseTime}ms (SLOW)`);
    }
  }
  
  if (supabaseToAppTime) {
    if (supabaseToAppTime < 1000) {
      console.log(`✅ Supabase → App: ${supabaseToAppTime}ms (EXCELLENT)`);
    } else if (supabaseToAppTime < 3000) {
      console.log(`⚠️  Supabase → App: ${supabaseToAppTime}ms (ACCEPTABLE)`);
    } else {
      console.log(`❌ Supabase → App: ${supabaseToAppTime}ms (SLOW)`);
    }
  }
  
  // Overall Status
  const allTestsPassed = testResults.appToSupabase.every(t => t.status === 'SUCCESS') &&
                        testResults.supabaseToApp.every(t => t.status === 'SUCCESS');
  
  console.log('\n🎉 OVERALL STATUS');
  console.log('================');
  
  if (allTestsPassed) {
    console.log('✅ BIDIRECTIONAL SYNC: PERFECT!');
    console.log('🚀 Ready for production deployment');
  } else {
    console.log('❌ BIDIRECTIONAL SYNC: ISSUES DETECTED');
    console.log('🔧 Review test results and fix issues');
  }
}

// =============================================
// QUICK SYNC CHECK
// =============================================

function quickSyncCheck() {
  console.log('\n⚡ QUICK SYNC STATUS CHECK');
  console.log('=========================');
  
  // Check if real-time indicators are present
  const indicators = document.querySelectorAll('[class*="real-time"], [class*="realtime"]');
  console.log(`📡 Real-time indicators found: ${indicators.length}`);
  
  // Check console for real-time messages
  console.log('🔍 Check browser console for real-time subscription messages');
  console.log('   Look for: "Successfully subscribed to [table] changes"');
  
  // Check Supabase client
  if (window.supabase) {
    console.log('✅ Supabase client available');
    console.log('📊 Real-time channels:', Object.keys(window.supabase.realtime?.channels || {}));
  } else {
    console.log('❌ Supabase client not found in window object');
  }
}

// =============================================
// EXPORT FUNCTIONS FOR MANUAL TESTING
// =============================================

window.dentoproSyncTest = {
  runFull: runComprehensiveSyncTest,
  quickCheck: quickSyncCheck,
  testConnection: testRealTimeConnection,
  testAppToSupabase: testAppToSupabaseSync,
  results: testResults
};

console.log('\n🎮 AVAILABLE COMMANDS:');
console.log('======================');
console.log('dentoproSyncTest.runFull()      - Run complete bidirectional test');
console.log('dentoproSyncTest.quickCheck()   - Quick sync status check');
console.log('dentoproSyncTest.testConnection() - Test real-time connection only');
console.log('dentoproSyncTest.results        - View test results');

console.log('\n🚀 Ready to test! Run: dentoproSyncTest.runFull()');
