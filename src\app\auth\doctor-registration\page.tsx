'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/useAuth';
import { SUBSCRIPTION_PLANS, formatPrice } from '@/lib/stripe';
import Link from 'next/link';

interface DoctorFormData {
  fullName: string;
  medicalLicense: string;
  clinicName: string;
  phoneNumber: string;
  whatsappNumber: string;
  selectedPlan: string;
}

export default function DoctorRegistrationPage() {
  const [formData, setFormData] = useState<DoctorFormData>({
    fullName: '',
    medicalLicense: '',
    clinicName: '',
    phoneNumber: '',
    whatsappNumber: '',
    selectedPlan: 'plan_trial'
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [step, setStep] = useState(1); // 1: Form, 2: Payment, 3: Success

  const { user, isAuthenticated } = useAuth();
  const router = useRouter();

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirectTo=/auth/doctor-registration');
    }
  }, [isAuthenticated, router]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  const handlePlanSelect = (planId: string) => {
    setFormData(prev => ({
      ...prev,
      selectedPlan: planId
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      // Step 1: Create doctor profile
      const response = await fetch('/api/auth/complete-doctor-registration', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          userId: user?.id
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Registration failed');
      }

      // Step 2: Handle payment based on selected plan
      if (formData.selectedPlan === 'plan_trial') {
        // Free trial - redirect to dashboard
        setStep(3);
        setTimeout(() => {
          router.push('/doctor/dashboard');
        }, 2000);
      } else {
        // Paid plan - redirect to payment
        setStep(2);
        await handlePayment(result.doctorId);
      }

    } catch (err: any) {
      setError(err.message || 'Registration failed');
    } finally {
      setLoading(false);
    }
  };

  const handlePayment = async (doctorId: string) => {
    try {
      const response = await fetch('/api/payments/create-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          doctorId,
          planId: formData.selectedPlan,
          trialDays: 7
        }),
      });

      const { clientSecret, subscriptionId } = await response.json();

      if (!response.ok) {
        throw new Error('Payment setup failed');
      }

      // Redirect to Stripe Checkout or handle payment
      window.location.href = `/payment/checkout?subscription_id=${subscriptionId}&client_secret=${clientSecret}`;

    } catch (err: any) {
      setError(err.message || 'Payment setup failed');
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Authentication Required</h2>
          <p className="text-gray-600 mb-6">Please log in to complete your doctor registration.</p>
          <Link href="/auth/login">
            <Button>Go to Login</Button>
          </Link>
        </div>
      </div>
    );
  }

  if (step === 3) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Welcome to DentoPro!</h2>
          <p className="text-gray-600 mb-6">
            Your doctor account has been created successfully. Your 7-day free trial has started.
          </p>
          <Button onClick={() => router.push('/doctor/dashboard')} className="w-full">
            Go to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Complete Your Doctor Registration
          </h2>
          <p className="text-lg text-gray-600">
            Set up your clinic profile and choose your subscription plan
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="grid grid-cols-1 lg:grid-cols-2">
            {/* Form Section */}
            <div className="p-8">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">Doctor Information</h3>
              
              {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
                  {error}
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-2">
                    Doctor's Full Name *
                  </label>
                  <input
                    id="fullName"
                    name="fullName"
                    type="text"
                    required
                    value={formData.fullName}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Dr. John Smith"
                  />
                </div>

                <div>
                  <label htmlFor="medicalLicense" className="block text-sm font-medium text-gray-700 mb-2">
                    Medical License Number *
                  </label>
                  <input
                    id="medicalLicense"
                    name="medicalLicense"
                    type="text"
                    required
                    value={formData.medicalLicense}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="ML123456789"
                  />
                </div>

                <div>
                  <label htmlFor="clinicName" className="block text-sm font-medium text-gray-700 mb-2">
                    Clinic Name *
                  </label>
                  <input
                    id="clinicName"
                    name="clinicName"
                    type="text"
                    required
                    value={formData.clinicName}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Smith Dental Clinic"
                  />
                </div>

                <div>
                  <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number *
                  </label>
                  <input
                    id="phoneNumber"
                    name="phoneNumber"
                    type="tel"
                    required
                    value={formData.phoneNumber}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="****** 567 8900"
                  />
                </div>

                <div>
                  <label htmlFor="whatsappNumber" className="block text-sm font-medium text-gray-700 mb-2">
                    WhatsApp Number (Optional)
                  </label>
                  <input
                    id="whatsappNumber"
                    name="whatsappNumber"
                    type="tel"
                    value={formData.whatsappNumber}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="****** 567 8900"
                  />
                </div>

                <Button
                  type="submit"
                  disabled={loading}
                  className="w-full"
                  size="lg"
                >
                  {loading ? 'Setting up your account...' : 'Start 7-Day Free Trial'}
                </Button>
              </form>
            </div>

            {/* Subscription Plans Section */}
            <div className="bg-gray-50 p-8">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">Choose Your Plan</h3>
              
              <div className="space-y-4">
                {Object.entries(SUBSCRIPTION_PLANS).map(([key, plan]) => (
                  <div
                    key={plan.id}
                    className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                      formData.selectedPlan === plan.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handlePlanSelect(plan.id)}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-lg font-semibold text-gray-900">{plan.name}</h4>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-gray-900">
                          {plan.price === 0 ? 'Free' : formatPrice(plan.price)}
                        </div>
                        {plan.price > 0 && (
                          <div className="text-sm text-gray-500">per month</div>
                        )}
                      </div>
                    </div>
                    
                    <ul className="text-sm text-gray-600 space-y-1">
                      {plan.features.slice(0, 4).map((feature, index) => (
                        <li key={index} className="flex items-center">
                          <svg className="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          {feature}
                        </li>
                      ))}
                      {plan.features.length > 4 && (
                        <li className="text-blue-600">+ {plan.features.length - 4} more features</li>
                      )}
                    </ul>

                    {plan.id === 'plan_trial' && (
                      <div className="mt-3 text-sm text-blue-600 font-medium">
                        ⭐ Recommended for new users
                      </div>
                    )}
                  </div>
                ))}
              </div>

              <div className="mt-6 text-sm text-gray-500">
                <p>✅ 7-day free trial for all plans</p>
                <p>✅ Cancel anytime</p>
                <p>✅ No setup fees</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
