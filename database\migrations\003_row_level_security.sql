-- DentoPro SaaS Platform - Row Level Security
-- Migration: 003_row_level_security.sql
-- Description: Implement Row Level Security for multi-tenant data isolation

-- =============================================
-- ENABLE ROW LEVEL SECURITY
-- =============================================

-- Enable RLS on all doctor-specific tables
ALTER TABLE clinic_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE patients ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE treatments ENABLE ROW LEVEL SECURITY;
ALTER TABLE materials ENABLE ROW LEVEL SECURITY;
ALTER TABLE material_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE prescriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE file_uploads ENABLE ROW LEVEL SECURITY;
ALTER TABLE platform_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE license_tracking ENABLE ROW LEVEL SECURITY;

-- =============================================
-- RLS POLICIES FOR SUPER ADMINS
-- =============================================

-- Super admins can access all data (no restrictions)
CREATE POLICY super_admin_all_access_clinic_settings ON clinic_settings
    FOR ALL TO authenticated
    USING (auth.jwt() ->> 'role' = 'super_admin');

CREATE POLICY super_admin_all_access_patients ON patients
    FOR ALL TO authenticated
    USING (auth.jwt() ->> 'role' = 'super_admin');

CREATE POLICY super_admin_all_access_appointments ON appointments
    FOR ALL TO authenticated
    USING (auth.jwt() ->> 'role' = 'super_admin');

CREATE POLICY super_admin_all_access_treatments ON treatments
    FOR ALL TO authenticated
    USING (auth.jwt() ->> 'role' = 'super_admin');

CREATE POLICY super_admin_all_access_materials ON materials
    FOR ALL TO authenticated
    USING (auth.jwt() ->> 'role' = 'super_admin');

CREATE POLICY super_admin_all_access_material_logs ON material_logs
    FOR ALL TO authenticated
    USING (auth.jwt() ->> 'role' = 'super_admin');

CREATE POLICY super_admin_all_access_prescriptions ON prescriptions
    FOR ALL TO authenticated
    USING (auth.jwt() ->> 'role' = 'super_admin');

CREATE POLICY super_admin_all_access_invoices ON invoices
    FOR ALL TO authenticated
    USING (auth.jwt() ->> 'role' = 'super_admin');

CREATE POLICY super_admin_all_access_notifications ON notifications
    FOR ALL TO authenticated
    USING (auth.jwt() ->> 'role' = 'super_admin');

CREATE POLICY super_admin_all_access_file_uploads ON file_uploads
    FOR ALL TO authenticated
    USING (auth.jwt() ->> 'role' = 'super_admin');

-- =============================================
-- RLS POLICIES FOR DOCTORS
-- =============================================

-- Doctors can only access their own clinic data
CREATE POLICY doctor_own_clinic_settings ON clinic_settings
    FOR ALL TO authenticated
    USING (
        auth.jwt() ->> 'role' = 'doctor' AND 
        doctor_id = (auth.jwt() ->> 'doctor_id')::UUID
    );

CREATE POLICY doctor_own_patients ON patients
    FOR ALL TO authenticated
    USING (
        auth.jwt() ->> 'role' = 'doctor' AND 
        doctor_id = (auth.jwt() ->> 'doctor_id')::UUID
    );

CREATE POLICY doctor_own_appointments ON appointments
    FOR ALL TO authenticated
    USING (
        auth.jwt() ->> 'role' = 'doctor' AND 
        doctor_id = (auth.jwt() ->> 'doctor_id')::UUID
    );

CREATE POLICY doctor_own_treatments ON treatments
    FOR ALL TO authenticated
    USING (
        auth.jwt() ->> 'role' = 'doctor' AND 
        doctor_id = (auth.jwt() ->> 'doctor_id')::UUID
    );

CREATE POLICY doctor_own_materials ON materials
    FOR ALL TO authenticated
    USING (
        auth.jwt() ->> 'role' = 'doctor' AND 
        doctor_id = (auth.jwt() ->> 'doctor_id')::UUID
    );

CREATE POLICY doctor_own_material_logs ON material_logs
    FOR ALL TO authenticated
    USING (
        auth.jwt() ->> 'role' = 'doctor' AND 
        doctor_id = (auth.jwt() ->> 'doctor_id')::UUID
    );

CREATE POLICY doctor_own_prescriptions ON prescriptions
    FOR ALL TO authenticated
    USING (
        auth.jwt() ->> 'role' = 'doctor' AND 
        doctor_id = (auth.jwt() ->> 'doctor_id')::UUID
    );

CREATE POLICY doctor_own_invoices ON invoices
    FOR ALL TO authenticated
    USING (
        auth.jwt() ->> 'role' = 'doctor' AND 
        doctor_id = (auth.jwt() ->> 'doctor_id')::UUID
    );

CREATE POLICY doctor_own_notifications ON notifications
    FOR ALL TO authenticated
    USING (
        auth.jwt() ->> 'role' = 'doctor' AND 
        (doctor_id = (auth.jwt() ->> 'doctor_id')::UUID OR 
         (recipient_type = 'doctor' AND recipient_id = (auth.jwt() ->> 'doctor_id')::UUID))
    );

CREATE POLICY doctor_own_file_uploads ON file_uploads
    FOR ALL TO authenticated
    USING (
        auth.jwt() ->> 'role' = 'doctor' AND 
        doctor_id = (auth.jwt() ->> 'doctor_id')::UUID
    );

-- =============================================
-- RLS POLICIES FOR PATIENTS
-- =============================================

-- Patients can only view their own records (read-only)
CREATE POLICY patient_own_record ON patients
    FOR SELECT TO authenticated
    USING (
        auth.jwt() ->> 'role' = 'patient' AND 
        id = (auth.jwt() ->> 'patient_id')::UUID
    );

CREATE POLICY patient_own_appointments ON appointments
    FOR SELECT TO authenticated
    USING (
        auth.jwt() ->> 'role' = 'patient' AND 
        patient_id = (auth.jwt() ->> 'patient_id')::UUID
    );

CREATE POLICY patient_own_treatments ON treatments
    FOR SELECT TO authenticated
    USING (
        auth.jwt() ->> 'role' = 'patient' AND 
        patient_id = (auth.jwt() ->> 'patient_id')::UUID
    );

CREATE POLICY patient_own_prescriptions ON prescriptions
    FOR SELECT TO authenticated
    USING (
        auth.jwt() ->> 'role' = 'patient' AND 
        patient_id = (auth.jwt() ->> 'patient_id')::UUID
    );

CREATE POLICY patient_own_invoices ON invoices
    FOR SELECT TO authenticated
    USING (
        auth.jwt() ->> 'role' = 'patient' AND 
        patient_id = (auth.jwt() ->> 'patient_id')::UUID
    );

CREATE POLICY patient_own_notifications ON notifications
    FOR SELECT TO authenticated
    USING (
        auth.jwt() ->> 'role' = 'patient' AND 
        recipient_type = 'patient' AND 
        recipient_id = (auth.jwt() ->> 'patient_id')::UUID
    );

CREATE POLICY patient_own_files ON file_uploads
    FOR SELECT TO authenticated
    USING (
        auth.jwt() ->> 'role' = 'patient' AND 
        patient_id = (auth.jwt() ->> 'patient_id')::UUID
    );

-- Patients can insert new appointment requests (for online booking)
CREATE POLICY patient_book_appointment ON appointments
    FOR INSERT TO authenticated
    WITH CHECK (
        auth.jwt() ->> 'role' = 'patient' AND 
        patient_id = (auth.jwt() ->> 'patient_id')::UUID AND
        source = 'patient_online'
    );

-- =============================================
-- ANALYTICS AND MONITORING POLICIES
-- =============================================

-- Platform analytics - Super admins see all, doctors see their own
CREATE POLICY platform_analytics_access ON platform_analytics
    FOR SELECT TO authenticated
    USING (
        auth.jwt() ->> 'role' = 'super_admin' OR
        (auth.jwt() ->> 'role' = 'doctor' AND 
         (doctor_id IS NULL OR doctor_id = (auth.jwt() ->> 'doctor_id')::UUID))
    );

-- License tracking - Super admins see all, doctors see their own
CREATE POLICY license_tracking_access ON license_tracking
    FOR ALL TO authenticated
    USING (
        auth.jwt() ->> 'role' = 'super_admin' OR
        (auth.jwt() ->> 'role' = 'doctor' AND 
         doctor_id = (auth.jwt() ->> 'doctor_id')::UUID)
    );

-- =============================================
-- HELPER FUNCTIONS FOR RLS
-- =============================================

-- Function to get current user's doctor_id from JWT
CREATE OR REPLACE FUNCTION auth.current_doctor_id()
RETURNS UUID AS $$
BEGIN
    RETURN (auth.jwt() ->> 'doctor_id')::UUID;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get current user's patient_id from JWT
CREATE OR REPLACE FUNCTION auth.current_patient_id()
RETURNS UUID AS $$
BEGIN
    RETURN (auth.jwt() ->> 'patient_id')::UUID;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get current user's role from JWT
CREATE OR REPLACE FUNCTION auth.current_user_role()
RETURNS TEXT AS $$
BEGIN
    RETURN auth.jwt() ->> 'role';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if current user is super admin
CREATE OR REPLACE FUNCTION auth.is_super_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN auth.jwt() ->> 'role' = 'super_admin';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if current user is doctor
CREATE OR REPLACE FUNCTION auth.is_doctor()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN auth.jwt() ->> 'role' = 'doctor';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if current user is patient
CREATE OR REPLACE FUNCTION auth.is_patient()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN auth.jwt() ->> 'role' = 'patient';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- SECURITY VIEWS FOR SAFE DATA ACCESS
-- =============================================

-- View for doctors to see their patient summary
CREATE VIEW doctor_patient_summary AS
SELECT 
    p.id,
    p.full_name,
    p.phone,
    p.email,
    p.date_of_birth,
    p.gender,
    COUNT(a.id) as total_appointments,
    MAX(a.appointment_date) as last_visit,
    COUNT(t.id) as total_treatments,
    SUM(i.total_amount) as total_billed
FROM patients p
LEFT JOIN appointments a ON p.id = a.patient_id
LEFT JOIN treatments t ON p.id = t.patient_id
LEFT JOIN invoices i ON p.id = i.patient_id
WHERE p.doctor_id = auth.current_doctor_id()
GROUP BY p.id, p.full_name, p.phone, p.email, p.date_of_birth, p.gender;

-- View for patients to see their own summary
CREATE VIEW patient_summary AS
SELECT 
    p.full_name,
    p.phone,
    p.email,
    p.date_of_birth,
    COUNT(a.id) as total_appointments,
    MAX(a.appointment_date) as last_visit,
    COUNT(t.id) as total_treatments,
    SUM(i.total_amount) as total_billed,
    SUM(CASE WHEN i.status = 'paid' THEN i.total_amount ELSE 0 END) as total_paid,
    SUM(CASE WHEN i.status != 'paid' THEN i.total_amount ELSE 0 END) as outstanding_balance
FROM patients p
LEFT JOIN appointments a ON p.id = a.patient_id
LEFT JOIN treatments t ON p.id = t.patient_id
LEFT JOIN invoices i ON p.id = i.patient_id
WHERE p.id = auth.current_patient_id()
GROUP BY p.full_name, p.phone, p.email, p.date_of_birth;

-- Enable RLS on views
ALTER VIEW doctor_patient_summary SET (security_barrier = true);
ALTER VIEW patient_summary SET (security_barrier = true);
