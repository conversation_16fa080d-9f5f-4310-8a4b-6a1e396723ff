'use client';

import { useEffect, useState, useCallback } from 'react';
import { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';
import { createClientSupabaseClient } from '@/lib/supabase';
import { useAuth } from './useAuth';

interface UseRealtimeSubscriptionOptions {
  table: string;
  filter?: string;
  event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
  schema?: string;
  enabled?: boolean;
  skipDoctorFilter?: boolean;  // Skip doctor filtering for platform admin data
}

interface RealtimeData<T> {
  data: T[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useRealtimeSubscription<T extends { [key: string]: any } = any>(
  options: UseRealtimeSubscriptionOptions
): RealtimeData<T> {
  const {
    table,
    filter,
    event = '*',
    schema = 'public',
    enabled = true,
    skipDoctorFilter = false
  } = options;

  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const { doctorId, isAuthenticated } = useAuth();
  const supabase = createClientSupabaseClient();

  // Fetch initial data
  const fetchData = useCallback(async () => {
    if (!enabled || !isAuthenticated) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      let query = supabase.from(table).select('*');

      // Apply doctor filter for multi-tenant isolation (skip for platform admin data)
      if (doctorId && table !== 'platform_admins' && !skipDoctorFilter) {
        query = query.eq('doctor_id', doctorId);
      }

      // Apply additional filters
      if (filter) {
        const [column, operator, value] = filter.split('.');
        switch (operator) {
          case 'eq':
            query = query.eq(column, value);
            break;
          case 'neq':
            query = query.neq(column, value);
            break;
          case 'gt':
            query = query.gt(column, value);
            break;
          case 'gte':
            query = query.gte(column, value);
            break;
          case 'lt':
            query = query.lt(column, value);
            break;
          case 'lte':
            query = query.lte(column, value);
            break;
          case 'like':
            query = query.like(column, value);
            break;
          case 'ilike':
            query = query.ilike(column, value);
            break;
          case 'in':
            query = query.in(column, value.split(','));
            break;
          default:
            query = query.eq(column, value);
        }
      }

      const { data: fetchedData, error: fetchError } = await query;

      if (fetchError) {
        throw fetchError;
      }

      setData(fetchedData || []);
    } catch (err) {
      console.error('Error fetching data:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  }, [table, filter, doctorId, enabled, isAuthenticated, supabase, skipDoctorFilter]);

  // Handle real-time changes
  const handleRealtimeChange = useCallback((
    payload: RealtimePostgresChangesPayload<T>
  ) => {
    console.log('Real-time change:', payload);

    switch (payload.eventType) {
      case 'INSERT':
        if (payload.new) {
          setData(prev => {
            // Check if item already exists to avoid duplicates
            const exists = prev.some(item => (item as any).id === (payload.new as any).id);
            if (exists) return prev;
            return [...prev, payload.new as T];
          });
        }
        break;

      case 'UPDATE':
        if (payload.new) {
          setData(prev => prev.map(item => 
            (item as any).id === (payload.new as any).id ? payload.new as T : item
          ));
        }
        break;

      case 'DELETE':
        if (payload.old) {
          setData(prev => prev.filter(item => 
            (item as any).id !== (payload.old as any).id
          ));
        }
        break;

      default:
        // For '*' event type, refetch data to be safe
        fetchData();
        break;
    }
  }, [fetchData]);

  useEffect(() => {
    if (!enabled || !isAuthenticated) {
      return;
    }

    // Fetch initial data
    fetchData();

    // Set up real-time subscription
    let channel: RealtimeChannel;

    const setupSubscription = () => {
      const channelName = `${table}_changes_${doctorId || 'global'}`;
      
      channel = supabase.channel(channelName);

      // Configure the subscription filter
      let subscriptionFilter = `${schema}:${table}`;
      
      // Add doctor filter for multi-tenant isolation (skip for platform admin data)
      if (doctorId && table !== 'platform_admins' && !skipDoctorFilter) {
        subscriptionFilter += `:doctor_id=eq.${doctorId}`;
      }

      // Add custom filter if provided
      if (filter) {
        subscriptionFilter += `:${filter}`;
      }

      // Configure subscription based on table and admin privileges
      const subscriptionConfig: any = {
        event: event,
        schema: schema,
        table: table
      };

      // Only add filter for non-admin users and specific tables
      if (doctorId && table !== 'platform_admins' && !skipDoctorFilter) {
        // Check if table has doctor_id column before filtering
        if (['patients', 'appointments', 'treatments', 'invoices'].includes(table)) {
          subscriptionConfig.filter = `doctor_id=eq.${doctorId}`;
        }
      }

      channel.on('postgres_changes' as any, subscriptionConfig, handleRealtimeChange);

      channel.subscribe((status) => {
        console.log(`Subscription status for ${table}:`, status);
        if (status === 'SUBSCRIBED') {
          console.log(`✅ Successfully subscribed to ${table} changes`);
        } else if (status === 'CHANNEL_ERROR') {
          console.error(`❌ Error subscribing to ${table} changes`);
          // Don't set error for admin dashboard - just log it
          if (!skipDoctorFilter) {
            setError('Real-time subscription failed');
          }
        } else if (status === 'CLOSED') {
          console.log(`🔌 Subscription to ${table} closed`);
        }
      });
    };

    setupSubscription();

    // Cleanup function
    return () => {
      if (channel) {
        console.log(`Unsubscribing from ${table} changes`);
        supabase.removeChannel(channel);
      }
    };
  }, [
    table,
    filter,
    event,
    schema,
    enabled,
    isAuthenticated,
    doctorId,
    supabase,
    fetchData,
    handleRealtimeChange
  ]);

  return {
    data,
    loading,
    error,
    refetch: fetchData
  };
}

// Specialized hooks for common use cases
export function usePatients() {
  return useRealtimeSubscription<any>({
    table: 'patients',
    enabled: true
  });
}

export function useAppointments(date?: string) {
  const filter = date ? `appointment_date.eq.${date}` : undefined;
  return useRealtimeSubscription<any>({
    table: 'appointments',
    filter,
    enabled: true
  });
}

export function useTodayAppointments() {
  const today = new Date().toISOString().split('T')[0];
  return useAppointments(today);
}

export function useTreatments(patientId?: string) {
  const filter = patientId ? `patient_id.eq.${patientId}` : undefined;
  return useRealtimeSubscription<any>({
    table: 'treatments',
    filter,
    enabled: true
  });
}

export function useMaterials() {
  return useRealtimeSubscription<any>({
    table: 'materials',
    enabled: true
  });
}

export function useLowStockMaterials() {
  return useRealtimeSubscription<any>({
    table: 'materials',
    // This would need a custom filter or view for low stock
    enabled: true
  });
}

export function useInvoices(status?: string) {
  const filter = status ? `status.eq.${status}` : undefined;
  return useRealtimeSubscription<any>({
    table: 'invoices',
    filter,
    enabled: true
  });
}

export function usePrescriptions(patientId?: string) {
  const filter = patientId ? `patient_id.eq.${patientId}` : undefined;
  return useRealtimeSubscription<any>({
    table: 'prescriptions',
    filter,
    enabled: true
  });
}

export function useNotifications() {
  const { user } = useAuth();
  const filter = user ? `recipient_id.eq.${user.id}` : undefined;
  
  return useRealtimeSubscription<any>({
    table: 'notifications',
    filter,
    enabled: !!user
  });
}

// Hook for platform admin data (no doctor filtering)
export function usePlatformData<T extends { [key: string]: any } = any>(table: string) {
  return useRealtimeSubscription<T>({
    table,
    enabled: true,
    skipDoctorFilter: true  // Skip doctor filtering for platform admin data
  });
}

export default useRealtimeSubscription;
