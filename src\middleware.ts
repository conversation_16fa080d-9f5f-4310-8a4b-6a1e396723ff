// Next.js middleware for authentication and authorization
import { createServerClient } from '@supabase/ssr';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { Database } from '@/lib/supabase';

// Define protected routes and their required roles
const PROTECTED_ROUTES = {
  // Super Admin routes
  '/admin': ['super_admin'],
  '/admin/*': ['super_admin'],
  
  // Doctor routes
  '/doctor/dashboard': ['doctor', 'super_admin'],
  '/doctor/dashboard/*': ['doctor', 'super_admin'],
  '/dashboard': ['doctor', 'super_admin'], // Legacy support
  '/dashboard/*': ['doctor', 'super_admin'], // Legacy support
  '/patients': ['doctor', 'super_admin'],
  '/patients/*': ['doctor', 'super_admin'],
  '/appointments': ['doctor', 'super_admin'],
  '/appointments/*': ['doctor', 'super_admin'],
  '/treatments': ['doctor', 'super_admin'],
  '/treatments/*': ['doctor', 'super_admin'],
  '/inventory': ['doctor', 'super_admin'],
  '/inventory/*': ['doctor', 'super_admin'],
  '/prescriptions': ['doctor', 'super_admin'],
  '/prescriptions/*': ['doctor', 'super_admin'],
  '/invoices': ['doctor', 'super_admin'],
  '/invoices/*': ['doctor', 'super_admin'],
  '/settings': ['doctor', 'super_admin'],
  '/settings/*': ['doctor', 'super_admin'],
  
  // Patient routes
  '/patient': ['patient'],
  '/patient/*': ['patient'],
  '/my-appointments': ['patient'],
  '/my-treatments': ['patient'],
  '/my-prescriptions': ['patient'],
  '/my-invoices': ['patient'],
  
  // API routes
  '/api/admin/*': ['super_admin'],
  '/api/doctor/*': ['doctor', 'super_admin'],
  '/api/patient/*': ['patient', 'doctor', 'super_admin'],
};

// Public routes that don't require authentication
const PUBLIC_ROUTES = [
  '/',
  '/auth/login',
  '/auth/register',
  '/auth/callback',
  '/auth/reset-password',
  '/auth/verify-email',
  '/setup', // Admin setup page
  '/patient/book-appointment', // Public booking - anyone can book
  '/public/*',
  '/api/public/*',
  '/api/auth/*',
  '/api/setup/*', // Setup API endpoints
  '/api/webhooks/*',
  '/_next/*',
  '/favicon.ico',
  '/robots.txt',
  '/sitemap.xml'
];

// Get user role from user metadata or database
async function getUserRole(supabase: any, userId: string): Promise<string | null> {
  try {
    // First check user metadata
    const { data: { user } } = await supabase.auth.getUser();
    if (user?.user_metadata?.role) {
      return user.user_metadata.role;
    }
    if (user?.app_metadata?.role) {
      return user.app_metadata.role;
    }

    // Check platform_admins table
    const { data: admin } = await supabase
      .from('platform_admins')
      .select('id')
      .eq('id', userId)
      .single();
    
    if (admin) {
      return 'super_admin';
    }

    // Check doctors table
    const { data: doctor } = await supabase
      .from('doctors')
      .select('id')
      .eq('id', userId)
      .single();
    
    if (doctor) {
      return 'doctor';
    }

    // Check patients table
    const { data: patient } = await supabase
      .from('patients')
      .select('id')
      .eq('id', userId)
      .single();
    
    if (patient) {
      return 'patient';
    }

    return null;
  } catch (error) {
    console.error('Error getting user role:', error);
    return null;
  }
}

// Check if route is public
function isPublicRoute(pathname: string): boolean {
  return PUBLIC_ROUTES.some(route => {
    if (route.endsWith('/*')) {
      return pathname.startsWith(route.slice(0, -2));
    }
    return pathname === route;
  });
}

// Check if user has required role for route
function hasRequiredRole(pathname: string, userRole: string): boolean {
  for (const [route, allowedRoles] of Object.entries(PROTECTED_ROUTES)) {
    if (route.endsWith('/*')) {
      if (pathname.startsWith(route.slice(0, -2))) {
        return allowedRoles.includes(userRole);
      }
    } else if (pathname === route) {
      return allowedRoles.includes(userRole);
    }
  }
  
  // If route is not in protected routes, allow access
  return true;
}

// Get redirect URL based on user role
function getRedirectUrl(role: string, request: NextRequest): string {
  const baseUrl = request.nextUrl.origin;
  
  switch (role) {
    case 'super_admin':
      return `${baseUrl}/admin`;
    case 'doctor':
      return `${baseUrl}/doctor/dashboard`;
    case 'patient':
      return `${baseUrl}/patient`;
    default:
      return `${baseUrl}/auth/login`;
  }
}

export async function middleware(request: NextRequest) {
  try {
    const response = NextResponse.next();
    const pathname = request.nextUrl.pathname;



    // Skip middleware for static files, API routes, and critical public pages
    if (
      pathname.startsWith('/_next/') ||
      pathname.startsWith('/api/webhooks/') ||
      pathname.startsWith('/api/public/') ||
      pathname.startsWith('/api/setup/') ||
      pathname.startsWith('/api/auth/') ||
      pathname.startsWith('/api/debug/') ||
      pathname === '/auth/register' ||  // ALWAYS allow registration
      pathname === '/auth/login' ||     // ALWAYS allow login
      pathname === '/' ||               // ALWAYS allow home page
      pathname.includes('.') // Static files
    ) {
      return response;
    }

    // Create Supabase client with isolated session per browser
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            // Only get cookies for THIS specific request/browser
            return request.cookies.getAll();
          },
          setAll(cookiesToSet) {
            // Only set cookies for THIS specific response/browser
            cookiesToSet.forEach(({ name, value, options }) => {
              response.cookies.set(name, value, {
                ...options,
                // Ensure cookies are browser-specific
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'lax'
              });
            });
          },
        },
      }
    );

    // Get user for THIS browser session only (completely isolated)
    const { data: { user }, error } = await supabase.auth.getUser();

    // If there's an error getting user, only redirect if it's a real auth error
    // Don't redirect for missing sessions (user might not be logged in)
    if (error && error.message !== 'Auth session missing!') {
      console.error('Auth error:', error);
      if (pathname !== '/auth/login' && pathname !== '/auth/register') {
        return NextResponse.redirect(new URL('/auth/login', request.url));
      }
      return response;
    }

    // Handle public routes
    if (isPublicRoute(pathname)) {
      // Allow access to all public routes - let components handle smart behavior
      return response;
    }

    // Check if user is authenticated
    if (!user) {
      // Store the attempted URL to redirect after login
      const redirectUrl = new URL('/auth/login', request.url);
      redirectUrl.searchParams.set('redirectTo', pathname);
      return NextResponse.redirect(redirectUrl);
    }

    // Get user role
    const userRole = await getUserRole(supabase, user.id);
    
    if (!userRole) {
      console.error('User role not found for user:', user.id);
      if (pathname !== '/auth/login') {
        return NextResponse.redirect(new URL('/auth/login', request.url));
      }
      // If already on login page, just continue
      return response;
    }

    // Check if user has required role for the route
    if (!hasRequiredRole(pathname, userRole)) {
      console.warn(`Access denied: User ${user.id} with role ${userRole} tried to access ${pathname}`);

      // Redirect to appropriate dashboard based on role
      return NextResponse.redirect(new URL(getRedirectUrl(userRole, request), request.url));
    }

    // Add user info to headers for use in API routes
    response.headers.set('x-user-id', user.id);
    response.headers.set('x-user-role', userRole);
    response.headers.set('x-user-email', user.email || '');

    // Log access for audit purposes
    console.log(`Access granted: User ${user.id} (${userRole}) accessing ${pathname}`);

    return response;
  } catch (error) {
    console.error('Middleware error:', error);

    // In case of any error, redirect to login (but not if already on login page)
    if (request.nextUrl.pathname !== '/auth/login') {
      return NextResponse.redirect(new URL('/auth/login', request.url));
    }
    // If already on login page, just continue
    return NextResponse.next();
  }
}

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     * - setup (admin setup page)
     */
    '/((?!_next/static|_next/image|favicon.ico|public/|setup).*)',
  ],
};

export default middleware;
