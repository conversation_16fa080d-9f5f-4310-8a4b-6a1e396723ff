import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase-server';
import { stripe, SUBSCRIPTION_PLANS } from '@/lib/stripe';

export async function POST(request: NextRequest) {
  try {
    const { doctorId, planId, trialDays = 7 } = await request.json();

    if (!doctorId || !planId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const supabase = await createServerSupabaseClient();

    // Get doctor information
    const { data: doctor, error: doctorError } = await supabase
      .from('doctors')
      .select('stripe_customer_id, email, full_name')
      .eq('id', doctorId)
      .single();

    if (doctorError || !doctor) {
      return NextResponse.json(
        { error: 'Doctor not found' },
        { status: 404 }
      );
    }

    // Get plan configuration
    const planConfig = Object.values(SUBSCRIPTION_PLANS).find(p => p.id === planId);
    
    if (!planConfig) {
      return NextResponse.json(
        { error: 'Invalid plan' },
        { status: 400 }
      );
    }

    // For trial plan, no payment needed
    if (planId === 'plan_trial') {
      return NextResponse.json({
        success: true,
        message: 'Trial subscription activated',
        subscriptionId: null
      });
    }

    // Create Stripe price if it doesn't exist (for demo purposes)
    let priceId = planConfig.priceId;
    
    if (!priceId) {
      const price = await stripe.prices.create({
        unit_amount: Math.round(planConfig.price * 100), // Convert to cents
        currency: 'usd',
        recurring: {
          interval: 'month',
        },
        product_data: {
          name: planConfig.name,
          description: `DentoPro ${planConfig.name} - Monthly subscription`,
        },
      });
      priceId = price.id;
    }

    // Create Stripe subscription with trial
    const subscription = await stripe.subscriptions.create({
      customer: doctor.stripe_customer_id,
      items: [
        {
          price: priceId,
        },
      ],
      trial_period_days: trialDays,
      payment_behavior: 'default_incomplete',
      payment_settings: {
        save_default_payment_method: 'on_subscription',
      },
      expand: ['latest_invoice.payment_intent'],
      metadata: {
        doctorId: doctorId,
        planId: planId,
      },
    });

    // Update subscription in database
    const { error: updateError } = await supabase
      .from('subscriptions')
      .update({
        stripe_subscription_id: subscription.id,
        status: 'trial',
        current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
        current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('doctor_id', doctorId);

    if (updateError) {
      console.error('Subscription update error:', updateError);
    }

    // Get client secret for payment
    const invoice = subscription.latest_invoice as any;
    const paymentIntent = invoice?.payment_intent;

    return NextResponse.json({
      success: true,
      subscriptionId: subscription.id,
      clientSecret: paymentIntent?.client_secret,
      status: subscription.status,
      trialEnd: new Date(subscription.trial_end! * 1000).toISOString(),
    });

  } catch (error) {
    console.error('Subscription creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create subscription' },
      { status: 500 }
    );
  }
}
