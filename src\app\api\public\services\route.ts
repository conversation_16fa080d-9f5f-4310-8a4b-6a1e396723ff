import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          },
        },
      }
    );

    // Get THE doctor who owns this installation
    const { data: doctor, error: doctorError } = await supabase
      .from('doctors')
      .select('id, clinic_name, full_name')
      .eq('is_active', true)
      .single();

    if (doctorError || !doctor) {
      return NextResponse.json(
        { error: 'Doctor not available' },
        { status: 400 }
      );
    }

    // Get doctor's services/treatments
    const { data: services, error: servicesError } = await supabase
      .from('treatment_types')
      .select('*')
      .eq('doctor_id', doctor.id)
      .eq('is_active', true)
      .order('name');

    // If no custom services, return default dental services
    const defaultServices = [
      {
        id: 'general-consultation',
        name: 'General Consultation',
        description: 'Comprehensive dental examination and consultation',
        duration: 30,
        price: 100,
        category: 'consultation'
      },
      {
        id: 'cleaning',
        name: 'Dental Cleaning',
        description: 'Professional teeth cleaning and polishing',
        duration: 45,
        price: 150,
        category: 'preventive'
      },
      {
        id: 'filling',
        name: 'Dental Filling',
        description: 'Tooth restoration with composite or amalgam filling',
        duration: 60,
        price: 200,
        category: 'restorative'
      },
      {
        id: 'crown',
        name: 'Dental Crown',
        description: 'Custom crown placement for damaged teeth',
        duration: 90,
        price: 800,
        category: 'restorative'
      },
      {
        id: 'extraction',
        name: 'Tooth Extraction',
        description: 'Safe and comfortable tooth removal',
        duration: 45,
        price: 250,
        category: 'surgery'
      },
      {
        id: 'whitening',
        name: 'Teeth Whitening',
        description: 'Professional teeth whitening treatment',
        duration: 60,
        price: 300,
        category: 'cosmetic'
      }
    ];

    const availableServices = services && services.length > 0 ? services : defaultServices;

    return NextResponse.json({
      services: availableServices,
      doctor_name: doctor.full_name,
      clinic_name: doctor.clinic_name
    });

  } catch (error) {
    console.error('Services error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
