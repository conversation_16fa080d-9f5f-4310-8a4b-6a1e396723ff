import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, fullName, clinicName, licenseNumber } = body;

    if (!email || !fullName || !clinicName) {
      return NextResponse.json(
        { error: 'Email, full name, and clinic name are required' },
        { status: 400 }
      );
    }

    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          },
        },
      }
    );

    const validationErrors = [];
    const warnings = [];

    // 1. Check for duplicate email
    const { data: existingEmail } = await supabase
      .from('doctors')
      .select('id, email, full_name, clinic_name')
      .eq('email', email.toLowerCase())
      .single();

    if (existingEmail) {
      validationErrors.push({
        field: 'email',
        message: `This email is already registered by Dr. ${existingEmail.full_name} at ${existingEmail.clinic_name}`,
        type: 'duplicate_email'
      });
    }

    // 2. Check for duplicate license number (if provided)
    if (licenseNumber) {
      const { data: existingLicense } = await supabase
        .from('doctors')
        .select('id, license_number, full_name, clinic_name')
        .eq('license_number', licenseNumber.toUpperCase())
        .single();

      if (existingLicense) {
        validationErrors.push({
          field: 'licenseNumber',
          message: `This license number is already registered by Dr. ${existingLicense.full_name} at ${existingLicense.clinic_name}`,
          type: 'duplicate_license'
        });
      }
    }

    // 3. Check for similar doctor names
    const { data: similarNames } = await supabase
      .from('doctors')
      .select('id, full_name, clinic_name, email')
      .ilike('full_name', `%${fullName.trim()}%`);

    if (similarNames && similarNames.length > 0) {
      const exactMatch = similarNames.find(doc => 
        doc.full_name.toLowerCase() === fullName.toLowerCase()
      );

      if (exactMatch) {
        validationErrors.push({
          field: 'fullName',
          message: `A doctor with the name "${exactMatch.full_name}" is already registered at ${exactMatch.clinic_name}`,
          type: 'duplicate_name'
        });
      } else if (similarNames.length > 0) {
        warnings.push({
          field: 'fullName',
          message: `Similar doctor names found: ${similarNames.map(d => `${d.full_name} (${d.clinic_name})`).join(', ')}`,
          type: 'similar_name'
        });
      }
    }

    // 4. Check for similar clinic names
    const { data: similarClinics } = await supabase
      .from('doctors')
      .select('id, clinic_name, full_name, clinic_slug')
      .ilike('clinic_name', `%${clinicName.trim()}%`);

    if (similarClinics && similarClinics.length > 0) {
      const exactMatch = similarClinics.find(clinic => 
        clinic.clinic_name.toLowerCase() === clinicName.toLowerCase()
      );

      if (exactMatch) {
        warnings.push({
          field: 'clinicName',
          message: `A clinic named "${exactMatch.clinic_name}" already exists (Dr. ${exactMatch.full_name}). Your clinic will get a unique booking URL.`,
          type: 'duplicate_clinic_name',
          suggestedSlug: `${clinicName.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '')}-2`
        });
      } else if (similarClinics.length > 0) {
        warnings.push({
          field: 'clinicName',
          message: `Similar clinic names found: ${similarClinics.map(c => `${c.clinic_name} (${c.full_name})`).join(', ')}`,
          type: 'similar_clinic_name'
        });
      }
    }

    // 5. Generate suggested clinic slug
    const baseSlug = clinicName.toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .trim();

    const { data: existingSlugs } = await supabase
      .from('doctors')
      .select('clinic_slug')
      .like('clinic_slug', `${baseSlug}%`);

    let suggestedSlug = baseSlug;
    if (existingSlugs && existingSlugs.length > 0) {
      const slugNumbers = existingSlugs
        .map(s => s.clinic_slug)
        .filter(slug => slug.startsWith(baseSlug))
        .map(slug => {
          const match = slug.match(/-(\d+)$/);
          return match ? parseInt(match[1]) : (slug === baseSlug ? 1 : 0);
        })
        .filter(num => num > 0);

      const nextNumber = slugNumbers.length > 0 ? Math.max(...slugNumbers) + 1 : 2;
      suggestedSlug = `${baseSlug}-${nextNumber}`;
    }

    // Return validation results
    return NextResponse.json({
      valid: validationErrors.length === 0,
      errors: validationErrors,
      warnings: warnings,
      suggestions: {
        clinicSlug: suggestedSlug,
        bookingUrl: `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/book/${suggestedSlug}`
      }
    });

  } catch (error) {
    console.error('Validation error:', error);
    return NextResponse.json(
      { error: 'Validation failed' },
      { status: 500 }
    );
  }
}
