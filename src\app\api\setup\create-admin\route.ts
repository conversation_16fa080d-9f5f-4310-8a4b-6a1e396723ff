import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { Database } from '@/lib/supabase';
import * as bcrypt from 'bcryptjs';

export async function POST(request: NextRequest) {
  try {
    console.log('🔧 Admin creation API called');

    const body = await request.json();
    const { email, password, fullName } = body;

    console.log('📝 Received data:', { email, fullName, passwordLength: password?.length });

    // Validate input
    if (!email || !password || !fullName) {
      return NextResponse.json(
        { error: 'Email, password, and full name are required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Validate password strength
    if (password.length < 8) {
      return NextResponse.json(
        { error: 'Password must be at least 8 characters long' },
        { status: 400 }
      );
    }

    // Create Supabase client with service role
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        cookies: {
          getAll() { return []; },
          setAll() {},
        },
      }
    );

    // Check if any admin already exists
    const { data: existingAdmins, error: checkError } = await supabase
      .from('platform_admins')
      .select('id')
      .limit(1);

    if (checkError) {
      console.error('Error checking existing admins:', checkError);
      return NextResponse.json(
        { error: 'Database error' },
        { status: 500 }
      );
    }

    // Only allow creating admin if no admins exist (security measure)
    if (existingAdmins && existingAdmins.length > 0) {
      return NextResponse.json(
        { error: 'Admin user already exists. This endpoint is only for initial setup.' },
        { status: 403 }
      );
    }

    // Hash the password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Create admin user in Supabase Auth
    const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
      user_metadata: {
        role: 'super_admin',
        full_name: fullName
      },
      app_metadata: {
        role: 'super_admin'
      }
    });

    if (authError) {
      console.error('Error creating auth user:', authError);
      return NextResponse.json(
        { error: `Failed to create auth user: ${authError.message}` },
        { status: 500 }
      );
    }

    if (!authUser.user) {
      return NextResponse.json(
        { error: 'Failed to create auth user' },
        { status: 500 }
      );
    }

    // Create admin record in platform_admins table
    const { data: adminData, error: adminError } = await supabase
      .from('platform_admins')
      .insert({
        id: authUser.user.id,
        email,
        password_hash: passwordHash,
        full_name: fullName,
        role: 'super_admin',
        permissions: {},
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (adminError) {
      console.error('Error creating admin record:', adminError);
      
      // Clean up auth user if admin record creation failed
      await supabase.auth.admin.deleteUser(authUser.user.id);
      
      return NextResponse.json(
        { error: `Failed to create admin record: ${adminError.message}` },
        { status: 500 }
      );
    }

    console.log('✅ Super Admin created successfully:', {
      id: adminData.id,
      email: adminData.email,
      fullName: adminData.full_name
    });

    return NextResponse.json({
      success: true,
      message: 'Super Admin created successfully',
      admin: {
        id: adminData.id,
        email: adminData.email,
        fullName: adminData.full_name,
        role: adminData.role
      }
    });

  } catch (error) {
    console.error('❌ Create admin error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
