-- QUICK FIX FOR EMAIL CONFIRMATION ISSUE
-- Run this in Supabase SQL Editor to manually confirm emails

-- 1. Check current unconfirmed users
SELECT 
  id, 
  email, 
  email_confirmed_at,
  created_at
FROM auth.users 
WHERE email_confirmed_at IS NULL
ORDER BY created_at DESC;

-- 2. Manually confirm all unconfirmed emails (FOR DEVELOPMENT ONLY)
UPDATE auth.users 
SET 
  email_confirmed_at = NOW(),
  updated_at = NOW()
WHERE email_confirmed_at IS NULL;

-- 3. Verify the fix worked
SELECT 
  id, 
  email, 
  email_confirmed_at,
  'Email confirmed successfully' as status
FROM auth.users 
WHERE email_confirmed_at IS NOT NULL
ORDER BY created_at DESC;

-- 4. Check if doctors table has the registered doctor
SELECT 
  id,
  email,
  full_name,
  clinic_name,
  subscription_status,
  created_at
FROM doctors
ORDER BY created_at DESC;

SELECT 'Email confirmation fix completed! Try logging in now.' as result;
