# 🗄️ DentoPro Database Schema

## Overview
This document outlines the complete database schema for the DentoPro SaaS platform, designed with multi-tenant architecture and data isolation for individual doctor clinics.

## Architecture Principles

### Multi-Tenant Strategy
- **Shared Database, Isolated Data**: All doctors share the same database instance but data is completely isolated
- **Row-Level Security (RLS)**: PostgreSQL RLS ensures doctors can only access their own data
- **Tenant Identification**: Every table includes `doctor_id` for data isolation
- **Super Admin Access**: Platform administrators can access all data for management purposes

### Data Isolation Levels
1. **Platform Level**: Super admin data (platform_admins, platform_analytics)
2. **Doctor Level**: Doctor-specific data (patients, appointments, treatments)
3. **Shared Level**: Reference data (subscription plans, system settings)

## Core Tables

### 1. Platform Management Tables

#### platform_admins
Super administrators who manage the entire platform.
```sql
CREATE TABLE platform_admins (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VA<PERSON>HA<PERSON>(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'super_admin',
    permissions JSONB DEFAULT '{}',
    last_login TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### doctors
Individual doctor accounts and clinic information.
```sql
CREATE TABLE doctors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    license_number VARCHAR(100) UNIQUE NOT NULL,
    clinic_name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    whatsapp VARCHAR(20),
    subscription_plan VARCHAR(50) NOT NULL DEFAULT 'trial',
    subscription_status VARCHAR(50) NOT NULL DEFAULT 'trial',
    domain VARCHAR(255),
    trial_end_date TIMESTAMP,
    approved_by UUID REFERENCES platform_admins(id),
    approved_at TIMESTAMP,
    last_login TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### subscriptions
Subscription and billing information.
```sql
CREATE TABLE subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    plan_type VARCHAR(50) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    stripe_subscription_id VARCHAR(255),
    stripe_customer_id VARCHAR(255),
    current_period_start TIMESTAMP NOT NULL,
    current_period_end TIMESTAMP NOT NULL,
    trial_end TIMESTAMP,
    cancelled_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2. Doctor Clinic Tables

#### clinic_settings
Individual clinic configuration and preferences.
```sql
CREATE TABLE clinic_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    timezone VARCHAR(50) DEFAULT 'UTC',
    language VARCHAR(5) DEFAULT 'en',
    currency VARCHAR(3) DEFAULT 'USD',
    working_hours JSONB NOT NULL DEFAULT '{}',
    appointment_duration INTEGER DEFAULT 30,
    allow_online_booking BOOLEAN DEFAULT true,
    require_patient_verification BOOLEAN DEFAULT true,
    auto_reminders BOOLEAN DEFAULT true,
    whatsapp_notifications BOOLEAN DEFAULT false,
    email_notifications BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(doctor_id)
);
```

#### patients
Patient records for each clinic.
```sql
CREATE TABLE patients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    email VARCHAR(255),
    full_name VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    date_of_birth DATE,
    gender VARCHAR(10),
    address TEXT,
    emergency_contact_name VARCHAR(255),
    emergency_contact_phone VARCHAR(20),
    emergency_contact_relationship VARCHAR(100),
    medical_history JSONB DEFAULT '{}',
    dental_history JSONB DEFAULT '{}',
    allergies TEXT[],
    current_medications TEXT[],
    insurance_info JSONB DEFAULT '{}',
    is_verified BOOLEAN DEFAULT false,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(doctor_id, phone)
);
```

#### appointments
Appointment scheduling and management.
```sql
CREATE TABLE appointments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    patient_id UUID NOT NULL REFERENCES patients(id) ON DELETE CASCADE,
    appointment_date DATE NOT NULL,
    appointment_time TIME NOT NULL,
    duration INTEGER DEFAULT 30,
    appointment_type VARCHAR(50) NOT NULL,
    status VARCHAR(50) DEFAULT 'scheduled',
    notes TEXT,
    reminder_sent BOOLEAN DEFAULT false,
    source VARCHAR(50) DEFAULT 'doctor',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### treatments
Treatment records and procedures.
```sql
CREATE TABLE treatments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    patient_id UUID NOT NULL REFERENCES patients(id) ON DELETE CASCADE,
    appointment_id UUID REFERENCES appointments(id) ON DELETE SET NULL,
    procedure_name VARCHAR(255) NOT NULL,
    tooth_number VARCHAR(10),
    surface VARCHAR(50),
    diagnosis TEXT,
    treatment_plan TEXT,
    notes TEXT,
    cost DECIMAL(10,2),
    materials_used JSONB DEFAULT '[]',
    before_images TEXT[],
    after_images TEXT[],
    pain_score INTEGER CHECK (pain_score >= 0 AND pain_score <= 10),
    follow_up_required BOOLEAN DEFAULT false,
    follow_up_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### materials
Inventory management for dental materials.
```sql
CREATE TABLE materials (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    category VARCHAR(100) NOT NULL,
    unit VARCHAR(20) NOT NULL,
    current_stock DECIMAL(10,2) DEFAULT 0,
    low_threshold DECIMAL(10,2) NOT NULL,
    cost_per_unit DECIMAL(10,2),
    supplier VARCHAR(255),
    supplier_contact TEXT,
    expiry_date DATE,
    batch_number VARCHAR(100),
    storage_location VARCHAR(255),
    notes TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### material_logs
Track material usage and stock movements.
```sql
CREATE TABLE material_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    material_id UUID NOT NULL REFERENCES materials(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL, -- 'stock_in', 'stock_out', 'adjustment', 'expired'
    quantity DECIMAL(10,2) NOT NULL,
    reason TEXT NOT NULL,
    reference_id UUID, -- appointment_id or supplier reference
    reference_type VARCHAR(50), -- 'appointment', 'purchase', 'adjustment'
    cost DECIMAL(10,2),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### prescriptions
Digital prescriptions with QR codes.
```sql
CREATE TABLE prescriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    patient_id UUID NOT NULL REFERENCES patients(id) ON DELETE CASCADE,
    appointment_id UUID REFERENCES appointments(id) ON DELETE SET NULL,
    prescription_number VARCHAR(100) UNIQUE NOT NULL,
    medications JSONB NOT NULL DEFAULT '[]',
    notes TEXT,
    qr_code TEXT NOT NULL,
    issued_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    valid_until TIMESTAMP NOT NULL,
    is_dispensed BOOLEAN DEFAULT false,
    dispensed_at TIMESTAMP,
    dispensed_by VARCHAR(255)
);
```

#### invoices
Billing and payment management.
```sql
CREATE TABLE invoices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    patient_id UUID NOT NULL REFERENCES patients(id) ON DELETE CASCADE,
    appointment_id UUID REFERENCES appointments(id) ON DELETE SET NULL,
    invoice_number VARCHAR(100) UNIQUE NOT NULL,
    items JSONB NOT NULL DEFAULT '[]',
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0,
    tax_rate DECIMAL(5,2) DEFAULT 0,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    status VARCHAR(50) DEFAULT 'draft',
    due_date DATE,
    paid_at TIMESTAMP,
    payment_method VARCHAR(50),
    payment_reference VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Indexes for Performance

```sql
-- Doctor-based indexes for data isolation
CREATE INDEX idx_patients_doctor_id ON patients(doctor_id);
CREATE INDEX idx_appointments_doctor_id ON appointments(doctor_id);
CREATE INDEX idx_treatments_doctor_id ON treatments(doctor_id);
CREATE INDEX idx_materials_doctor_id ON materials(doctor_id);
CREATE INDEX idx_prescriptions_doctor_id ON prescriptions(doctor_id);
CREATE INDEX idx_invoices_doctor_id ON invoices(doctor_id);

-- Performance indexes
CREATE INDEX idx_appointments_date_time ON appointments(appointment_date, appointment_time);
CREATE INDEX idx_patients_phone ON patients(phone);
CREATE INDEX idx_patients_email ON patients(email);
CREATE INDEX idx_materials_low_stock ON materials(doctor_id, current_stock, low_threshold);
CREATE INDEX idx_prescriptions_valid ON prescriptions(valid_until, is_dispensed);
CREATE INDEX idx_invoices_status ON invoices(doctor_id, status);
```

### 3. Platform Analytics & Monitoring Tables

#### platform_analytics
Platform-wide usage and performance metrics.
```sql
CREATE TABLE platform_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    metric_type VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,2) NOT NULL,
    doctor_id UUID REFERENCES doctors(id) ON DELETE CASCADE,
    metadata JSONB DEFAULT '{}',
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### license_tracking
Anti-resale protection and usage monitoring.
```sql
CREATE TABLE license_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    domain VARCHAR(255) NOT NULL,
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    usage_stats JSONB DEFAULT '{}',
    violations JSONB DEFAULT '[]',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### system_logs
Comprehensive audit logging.
```sql
CREATE TABLE system_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    level VARCHAR(20) NOT NULL, -- 'info', 'warn', 'error', 'debug'
    message TEXT NOT NULL,
    user_id UUID,
    user_type VARCHAR(50), -- 'super_admin', 'doctor', 'patient'
    doctor_id UUID REFERENCES doctors(id) ON DELETE SET NULL,
    action VARCHAR(100),
    resource_type VARCHAR(100),
    resource_id UUID,
    ip_address INET,
    user_agent TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### notifications
System notifications and alerts.
```sql
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    recipient_type VARCHAR(50) NOT NULL, -- 'super_admin', 'doctor', 'patient'
    recipient_id UUID NOT NULL,
    doctor_id UUID REFERENCES doctors(id) ON DELETE CASCADE,
    type VARCHAR(100) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    priority VARCHAR(20) DEFAULT 'normal',
    metadata JSONB DEFAULT '{}',
    scheduled_for TIMESTAMP,
    sent_at TIMESTAMP,
    read_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 4. File Management Tables

#### file_uploads
Manage patient files, X-rays, and documents.
```sql
CREATE TABLE file_uploads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    patient_id UUID REFERENCES patients(id) ON DELETE CASCADE,
    appointment_id UUID REFERENCES appointments(id) ON DELETE SET NULL,
    treatment_id UUID REFERENCES treatments(id) ON DELETE SET NULL,
    file_name VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    file_size BIGINT NOT NULL,
    file_path TEXT NOT NULL,
    file_url TEXT,
    category VARCHAR(100), -- 'xray', 'photo', 'document', 'prescription'
    description TEXT,
    is_public BOOLEAN DEFAULT false,
    uploaded_by UUID NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Indexes for Performance

```sql
-- Doctor-based indexes for data isolation
CREATE INDEX idx_patients_doctor_id ON patients(doctor_id);
CREATE INDEX idx_appointments_doctor_id ON appointments(doctor_id);
CREATE INDEX idx_treatments_doctor_id ON treatments(doctor_id);
CREATE INDEX idx_materials_doctor_id ON materials(doctor_id);
CREATE INDEX idx_prescriptions_doctor_id ON prescriptions(doctor_id);
CREATE INDEX idx_invoices_doctor_id ON invoices(doctor_id);
CREATE INDEX idx_notifications_doctor_id ON notifications(doctor_id);
CREATE INDEX idx_file_uploads_doctor_id ON file_uploads(doctor_id);

-- Performance indexes
CREATE INDEX idx_appointments_date_time ON appointments(appointment_date, appointment_time);
CREATE INDEX idx_patients_phone ON patients(phone);
CREATE INDEX idx_patients_email ON patients(email);
CREATE INDEX idx_materials_low_stock ON materials(doctor_id, current_stock, low_threshold);
CREATE INDEX idx_prescriptions_valid ON prescriptions(valid_until, is_dispensed);
CREATE INDEX idx_invoices_status ON invoices(doctor_id, status);
CREATE INDEX idx_notifications_status ON notifications(recipient_id, status);
CREATE INDEX idx_system_logs_created_at ON system_logs(created_at);
CREATE INDEX idx_platform_analytics_metric ON platform_analytics(metric_type, recorded_at);
CREATE INDEX idx_license_tracking_doctor ON license_tracking(doctor_id, is_active);

-- Composite indexes for common queries
CREATE INDEX idx_appointments_doctor_date ON appointments(doctor_id, appointment_date);
CREATE INDEX idx_treatments_patient_date ON treatments(patient_id, created_at);
CREATE INDEX idx_material_logs_material_date ON material_logs(material_id, created_at);
```

## Row Level Security (RLS)

```sql
-- Enable RLS on all doctor-specific tables
ALTER TABLE patients ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE treatments ENABLE ROW LEVEL SECURITY;
ALTER TABLE materials ENABLE ROW LEVEL SECURITY;
ALTER TABLE material_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE prescriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE clinic_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE file_uploads ENABLE ROW LEVEL SECURITY;

-- RLS Policies (will be implemented in next phase)
-- Doctors can only access their own data
-- Super admins can access all data
-- Patients can only access their own records
```

## Data Relationships

### Primary Relationships
- **doctors** → **clinic_settings** (1:1)
- **doctors** → **patients** (1:many)
- **doctors** → **appointments** (1:many)
- **patients** → **appointments** (1:many)
- **appointments** → **treatments** (1:many)
- **doctors** → **materials** (1:many)
- **materials** → **material_logs** (1:many)

### Cross-References
- **treatments** ↔ **materials** (many:many via materials_used JSONB)
- **appointments** ↔ **prescriptions** (1:many)
- **appointments** ↔ **invoices** (1:many)
- **patients** ↔ **file_uploads** (1:many)

## Data Isolation Strategy

### Tenant Isolation
1. **Doctor ID Filtering**: Every query includes `doctor_id` filter
2. **RLS Enforcement**: PostgreSQL RLS prevents cross-tenant data access
3. **Application Layer**: Additional validation in API layer
4. **Super Admin Override**: Platform admins can access all data for support

### Security Measures
1. **Encrypted Sensitive Data**: Patient medical data encrypted at rest
2. **Audit Trail**: All actions logged in system_logs
3. **Access Control**: Role-based permissions with JWT tokens
4. **Domain Binding**: License tracking prevents unauthorized access
