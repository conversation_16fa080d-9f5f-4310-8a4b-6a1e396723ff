-- FIX BOTH ISSUES: Admin Dashboard + Unique Booking URLs
-- Run this in Supabase SQL Editor

-- 1. CHECK CURRENT DOCTORS AND THEIR CLINIC SLUGS
SELECT 
  id,
  email,
  full_name,
  clinic_name,
  clinic_slug,
  subscription_status,
  created_at
FROM doctors
ORDER BY created_at DESC;

-- 2. <PERSON>IX CLINIC SLUG GENERATION (Make sure it's unique)
-- First, let's see if the function exists
SELECT routine_name 
FROM information_schema.routines 
WHERE routine_name = 'generate_unique_clinic_slug';

-- 3. CREATE/UPDATE THE UNIQUE SLUG GENERATION FUNCTION
CREATE OR REPLACE FUNCTION generate_unique_clinic_slug(clinic_name_input VARCHAR(255), doctor_id_input UUID DEFAULT NULL)
RETURNS VARCHAR(255) AS $$
DECLARE
    base_slug VARCHAR(255);
    final_slug VARCHAR(255);
    counter INTEGER := 0;
    slug_exists BOOLEAN;
    random_suffix VARCHAR(10);
BEGIN
    -- Generate base slug from clinic name
    base_slug := LOWER(TRIM(clinic_name_input));
    base_slug := REGEXP_REPLACE(base_slug, '[^a-z0-9\s]', '', 'g');
    base_slug := REGEXP_REPLACE(base_slug, '\s+', '-', 'g');
    base_slug := TRIM(base_slug, '-');
    
    -- Ensure minimum length
    IF LENGTH(base_slug) < 3 THEN
        base_slug := 'clinic-' || base_slug;
    END IF;
    
    -- Add random suffix to make it more unique
    random_suffix := SUBSTRING(MD5(RANDOM()::TEXT) FROM 1 FOR 6);
    final_slug := base_slug || '-' || random_suffix;
    
    -- Check if slug exists (excluding current doctor if updating)
    SELECT EXISTS(
        SELECT 1 FROM doctors 
        WHERE clinic_slug = final_slug 
        AND (doctor_id_input IS NULL OR id != doctor_id_input)
    ) INTO slug_exists;
    
    -- If still exists (very unlikely), add timestamp
    IF slug_exists THEN
        final_slug := base_slug || '-' || EXTRACT(EPOCH FROM NOW())::INTEGER;
    END IF;
    
    RETURN final_slug;
END;
$$ LANGUAGE plpgsql;

-- 4. UPDATE ALL EXISTING DOCTORS WITH UNIQUE CLINIC SLUGS
UPDATE doctors
SET clinic_slug = generate_unique_clinic_slug(clinic_name, id)
WHERE clinic_slug IS NULL OR clinic_slug = '' OR clinic_slug = 'your-clinic';

-- 4.1. FORCE UPDATE ALL DOCTORS TO ENSURE UNIQUENESS
UPDATE doctors
SET clinic_slug = generate_unique_clinic_slug(clinic_name, id);

-- 5. VERIFY ADMIN DASHBOARD DATA ACCESS
-- Check if platform_admins table exists and has data
SELECT 'Checking platform_admins table...' as step;
SELECT * FROM platform_admins LIMIT 5;

-- 6. ENABLE ROW LEVEL SECURITY BYPASS FOR ADMIN QUERIES
-- Make sure admin can see all data
ALTER TABLE doctors ENABLE ROW LEVEL SECURITY;
ALTER TABLE patients ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;

-- Create policy for platform admins to see all data
DROP POLICY IF EXISTS "Platform admins can view all doctors" ON doctors;
CREATE POLICY "Platform admins can view all doctors" ON doctors
FOR SELECT TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM platform_admins 
    WHERE platform_admins.id = auth.uid()
  )
);

DROP POLICY IF EXISTS "Platform admins can view all patients" ON patients;
CREATE POLICY "Platform admins can view all patients" ON patients
FOR SELECT TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM platform_admins 
    WHERE platform_admins.id = auth.uid()
  )
);

DROP POLICY IF EXISTS "Platform admins can view all appointments" ON appointments;
CREATE POLICY "Platform admins can view all appointments" ON appointments
FOR SELECT TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM platform_admins 
    WHERE platform_admins.id = auth.uid()
  )
);

-- 7. FINAL VERIFICATION
SELECT 'VERIFICATION RESULTS:' as title;

SELECT 
  'Total Doctors:' as metric,
  COUNT(*) as value
FROM doctors;

SELECT 
  'Doctors with Unique Slugs:' as metric,
  COUNT(*) as value
FROM doctors 
WHERE clinic_slug IS NOT NULL AND clinic_slug != '' AND clinic_slug != 'your-clinic';

SELECT 
  'Sample Unique Booking URLs:' as metric,
  CONCAT('http://localhost:3000/book/', clinic_slug) as booking_url,
  clinic_name,
  full_name
FROM doctors 
WHERE clinic_slug IS NOT NULL 
LIMIT 3;

SELECT 'Admin Dashboard Fix Complete!' as result;
