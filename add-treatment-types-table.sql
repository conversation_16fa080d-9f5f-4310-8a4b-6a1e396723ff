-- Add treatment_types table for doctor's services
CREATE TABLE IF NOT EXISTS treatment_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    duration INTEGER NOT NULL DEFAULT 30, -- in minutes
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    category VARCHAR(100) DEFAULT 'general',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policy for treatment_types
ALTER TABLE treatment_types ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Doctors can manage their own treatment types" ON treatment_types
    FOR ALL USING (doctor_id = auth.uid());

CREATE POLICY "Public can view active treatment types" ON treatment_types
    FOR SELECT USING (is_active = true);

-- Add indexes for performance
CREATE INDEX idx_treatment_types_doctor_id ON treatment_types(doctor_id);
CREATE INDEX idx_treatment_types_active ON treatment_types(is_active);

-- Insert default treatment types for existing doctors
INSERT INTO treatment_types (doctor_id, name, description, duration, price, category)
SELECT 
    id as doctor_id,
    'General Consultation' as name,
    'Comprehensive dental examination and consultation' as description,
    30 as duration,
    100.00 as price,
    'consultation' as category
FROM doctors 
WHERE is_active = true
ON CONFLICT DO NOTHING;

INSERT INTO treatment_types (doctor_id, name, description, duration, price, category)
SELECT 
    id as doctor_id,
    'Dental Cleaning' as name,
    'Professional teeth cleaning and polishing' as description,
    45 as duration,
    150.00 as price,
    'preventive' as category
FROM doctors 
WHERE is_active = true
ON CONFLICT DO NOTHING;

INSERT INTO treatment_types (doctor_id, name, description, duration, price, category)
SELECT 
    id as doctor_id,
    'Dental Filling' as name,
    'Tooth restoration with composite or amalgam filling' as description,
    60 as duration,
    200.00 as price,
    'restorative' as category
FROM doctors 
WHERE is_active = true
ON CONFLICT DO NOTHING;
