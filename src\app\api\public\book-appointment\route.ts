import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      patientName,
      email,
      phone,
      preferredDate,
      preferredTime,
      treatmentType,
      notes
    } = body;

    // Validate required fields
    if (!patientName || !email || !phone || !preferredDate || !preferredTime) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          },
        },
      }
    );

    // Get THE doctor who owns this installation (single doctor license)
    // Each installation belongs to one doctor only
    const { data: doctor, error: doctorError } = await supabase
      .from('doctors')
      .select('id, clinic_name, full_name, phone, whatsapp')
      .eq('is_active', true)
      .single();

    if (doctorError || !doctor) {
      return NextResponse.json(
        { error: 'Doctor not available. Please contact clinic directly.' },
        { status: 400 }
      );
    }

    // Smart patient recognition - check by phone OR email (returning patient detection)
    let patientId: string;
    let isReturningPatient = false;

    const { data: existingPatient } = await supabase
      .from('patients')
      .select('id, full_name, email, phone')
      .eq('doctor_id', doctor.id)
      .or(`email.eq.${email},phone.eq.${phone}`)
      .single();

    if (existingPatient) {
      patientId = existingPatient.id;
      isReturningPatient = true;
      console.log(`🔄 Returning patient detected: ${existingPatient.full_name}`);
    } else {
      // Create new patient record
      const { data: newPatient, error: patientError } = await supabase
        .from('patients')
        .insert({
          doctor_id: doctor.id,
          email: email,
          full_name: patientName,
          phone: phone,
          is_verified: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select('id')
        .single();

      if (patientError || !newPatient) {
        console.error('Error creating patient:', patientError);
        return NextResponse.json(
          { error: 'Failed to create patient record' },
          { status: 500 }
        );
      }

      patientId = newPatient.id;
    }

    // Validate appointment time against doctor's working hours
    const appointmentDate = new Date(preferredDate);
    const dayOfWeek = appointmentDate.toLocaleDateString('en-US', { weekday: 'lowercase' });

    // Get doctor's working hours
    const { data: clinicSettings } = await supabase
      .from('clinic_settings')
      .select('working_hours, appointment_duration')
      .eq('doctor_id', doctor.id)
      .single();

    // Validate working hours (basic validation - can be enhanced)
    if (clinicSettings?.working_hours) {
      const daySchedule = clinicSettings.working_hours[dayOfWeek];
      if (!daySchedule?.enabled) {
        return NextResponse.json(
          { error: 'Selected day is not available. Please choose another date.' },
          { status: 400 }
        );
      }
    }

    // Check for conflicting appointments
    const { data: conflictingAppointments } = await supabase
      .from('appointments')
      .select('id')
      .eq('doctor_id', doctor.id)
      .eq('appointment_date', preferredDate)
      .eq('appointment_time', preferredTime)
      .neq('status', 'cancelled');

    if (conflictingAppointments && conflictingAppointments.length > 0) {
      return NextResponse.json(
        { error: 'This time slot is already booked. Please select another time.' },
        { status: 400 }
      );
    }

    // Create appointment
    const appointmentDateTime = new Date(`${preferredDate}T${preferredTime}`);

    const { data: appointment, error: appointmentError } = await supabase
      .from('appointments')
      .insert({
        doctor_id: doctor.id,
        patient_id: patientId,
        appointment_date: preferredDate,
        appointment_time: preferredTime,
        treatment_type: treatmentType || 'General Consultation',
        status: 'pending',
        notes: notes || '',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select('id')
      .single();

    if (appointmentError || !appointment) {
      console.error('Error creating appointment:', appointmentError);
      return NextResponse.json(
        { error: 'Failed to create appointment' },
        { status: 500 }
      );
    }

    // Create notification for the doctor
    await supabase
      .from('notifications')
      .insert({
        user_id: doctor.id,
        type: 'new_appointment',
        title: 'New Appointment Request',
        message: `New appointment request from ${patientName} for ${appointmentDateTime.toLocaleDateString()}`,
        data: {
          appointment_id: appointment.id,
          patient_name: patientName,
          appointment_date: preferredDate,
          appointment_time: preferredTime
        },
        is_read: false,
        created_at: new Date().toISOString()
      });

    return NextResponse.json({
      success: true,
      message: isReturningPatient
        ? `Welcome back! Your appointment has been scheduled.`
        : 'Appointment request submitted successfully. Welcome to our clinic!',
      appointment: {
        id: appointment.id,
        doctor_name: doctor.full_name,
        clinic_name: doctor.clinic_name,
        date: preferredDate,
        time: preferredTime,
        is_returning_patient: isReturningPatient
      }
    });

  } catch (error) {
    console.error('Booking error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
