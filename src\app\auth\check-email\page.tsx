'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { createClientSupabaseClient } from '@/lib/supabase';
import Link from 'next/link';

export default function CheckEmailPage() {
  const [email, setEmail] = useState('');
  const [resending, setResending] = useState(false);
  const [resent, setResent] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();
  const supabase = createClientSupabaseClient();

  useEffect(() => {
    const emailParam = searchParams.get('email');
    if (emailParam) {
      setEmail(decodeURIComponent(emailParam));
    }
  }, [searchParams]);

  const resendConfirmation = async () => {
    if (!email) return;
    
    setResending(true);
    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email,
      });

      if (error) {
        console.error('Resend error:', error);
        alert('Failed to resend confirmation email. Please try again.');
      } else {
        setResent(true);
      }
    } catch (error) {
      console.error('Resend failed:', error);
      alert('Failed to resend confirmation email. Please try again.');
    } finally {
      setResending(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        {/* Email Icon */}
        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        </div>

        {/* Header */}
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Check Your Email 📧
        </h1>
        <p className="text-gray-600 mb-6">
          We've sent a confirmation link to:
        </p>

        {/* Email Display */}
        <div className="bg-gray-50 rounded-lg p-3 mb-6">
          <p className="font-medium text-gray-900 break-all">
            {email || 'your email address'}
          </p>
        </div>

        {/* Instructions */}
        <div className="text-left bg-blue-50 rounded-lg p-4 mb-6">
          <h3 className="font-semibold text-blue-900 mb-2">Next Steps:</h3>
          <ol className="text-sm text-blue-800 space-y-1">
            <li>1. Check your email inbox</li>
            <li>2. Look for an email from DentoPro</li>
            <li>3. Click the confirmation link</li>
            <li>4. Return here to log in</li>
          </ol>
        </div>

        {/* Resend Button */}
        <div className="space-y-4">
          {!resent ? (
            <Button
              onClick={resendConfirmation}
              disabled={resending || !email}
              variant="outline"
              className="w-full"
            >
              {resending ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                  Resending...
                </div>
              ) : (
                'Resend Confirmation Email'
              )}
            </Button>
          ) : (
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
              <div className="flex items-center">
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                Confirmation email sent!
              </div>
            </div>
          )}

          <Button
            onClick={() => router.push('/auth/login')}
            className="w-full"
          >
            Continue to Login
          </Button>
        </div>

        {/* Help Text */}
        <div className="mt-6 text-xs text-gray-500 space-y-2">
          <p>
            Didn't receive the email? Check your spam folder or{' '}
            <button 
              onClick={resendConfirmation}
              className="text-blue-600 hover:text-blue-500 underline"
              disabled={resending}
            >
              resend confirmation
            </button>
          </p>
          <p>
            Need help?{' '}
            <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-500">
              Contact Support
            </a>
          </p>
        </div>

        {/* Back to Registration */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <p className="text-sm text-gray-600">
            Wrong email address?{' '}
            <Link href="/auth/register" className="text-blue-600 hover:text-blue-500 font-medium">
              Register again
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
