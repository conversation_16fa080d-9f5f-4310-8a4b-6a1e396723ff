// Data encryption utilities for HIPAA/GDPR compliance
import crypto from 'crypto';

// Encryption configuration
const ENCRYPTION_ALGORITHM = 'aes-256-gcm';
const KEY_LENGTH = 32; // 256 bits
const IV_LENGTH = 16; // 128 bits
const TAG_LENGTH = 16; // 128 bits

// Get encryption key from environment
function getEncryptionKey(): Buffer {
  const key = process.env.ENCRYPTION_KEY;
  if (!key) {
    throw new Error('ENCRYPTION_KEY environment variable is required');
  }
  
  if (key.length !== 64) { // 32 bytes = 64 hex characters
    throw new Error('ENCRYPTION_KEY must be 64 hex characters (32 bytes)');
  }
  
  return Buffer.from(key, 'hex');
}

// Generate a random encryption key (for setup)
export function generateEncryptionKey(): string {
  return crypto.randomBytes(KEY_LENGTH).toString('hex');
}

// Encrypt sensitive data
export function encrypt(plaintext: string): string {
  try {
    const key = getEncryptionKey();
    const iv = crypto.randomBytes(IV_LENGTH);
    const cipher = crypto.createCipher(ENCRYPTION_ALGORITHM, key);
    cipher.setAAD(Buffer.from('dentopro-saas', 'utf8'));
    
    let encrypted = cipher.update(plaintext, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const tag = cipher.getAuthTag();
    
    // Combine IV + encrypted data + auth tag
    const combined = iv.toString('hex') + encrypted + tag.toString('hex');
    
    return combined;
  } catch (error) {
    console.error('Encryption error:', error);
    throw new Error('Failed to encrypt data');
  }
}

// Decrypt sensitive data
export function decrypt(encryptedData: string): string {
  try {
    const key = getEncryptionKey();
    
    // Extract components
    const iv = Buffer.from(encryptedData.slice(0, IV_LENGTH * 2), 'hex');
    const tag = Buffer.from(encryptedData.slice(-TAG_LENGTH * 2), 'hex');
    const encrypted = encryptedData.slice(IV_LENGTH * 2, -TAG_LENGTH * 2);
    
    const decipher = crypto.createDecipher(ENCRYPTION_ALGORITHM, key);
    decipher.setAAD(Buffer.from('dentopro-saas', 'utf8'));
    decipher.setAuthTag(tag);
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  } catch (error) {
    console.error('Decryption error:', error);
    throw new Error('Failed to decrypt data');
  }
}

// Hash passwords securely
export async function hashPassword(password: string): Promise<string> {
  const bcrypt = await import('bcrypt');
  const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
  return bcrypt.hash(password, saltRounds);
}

// Verify password hash
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  const bcrypt = await import('bcrypt');
  return bcrypt.compare(password, hash);
}

// Generate secure random tokens
export function generateSecureToken(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex');
}

// Generate QR code data with encryption
export function generateSecureQRCode(data: any): string {
  const jsonData = JSON.stringify(data);
  const encrypted = encrypt(jsonData);
  
  // Add timestamp and signature for verification
  const timestamp = Date.now();
  const signature = crypto
    .createHmac('sha256', getEncryptionKey())
    .update(encrypted + timestamp)
    .digest('hex');
  
  return Buffer.from(JSON.stringify({
    data: encrypted,
    timestamp,
    signature
  })).toString('base64');
}

// Verify and decrypt QR code data
export function verifySecureQRCode(qrCode: string): any {
  try {
    const decoded = JSON.parse(Buffer.from(qrCode, 'base64').toString('utf8'));
    const { data, timestamp, signature } = decoded;
    
    // Verify signature
    const expectedSignature = crypto
      .createHmac('sha256', getEncryptionKey())
      .update(data + timestamp)
      .digest('hex');
    
    if (signature !== expectedSignature) {
      throw new Error('Invalid QR code signature');
    }
    
    // Check if QR code is not too old (24 hours)
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
    if (Date.now() - timestamp > maxAge) {
      throw new Error('QR code has expired');
    }
    
    // Decrypt and return data
    const decryptedData = decrypt(data);
    return JSON.parse(decryptedData);
  } catch (error) {
    console.error('QR code verification error:', error);
    throw new Error('Invalid or expired QR code');
  }
}

// Encrypt sensitive database fields
export class FieldEncryption {
  // Encrypt medical history
  static encryptMedicalHistory(history: any): string {
    return encrypt(JSON.stringify(history));
  }
  
  // Decrypt medical history
  static decryptMedicalHistory(encryptedHistory: string): any {
    const decrypted = decrypt(encryptedHistory);
    return JSON.parse(decrypted);
  }
  
  // Encrypt patient notes
  static encryptNotes(notes: string): string {
    return encrypt(notes);
  }
  
  // Decrypt patient notes
  static decryptNotes(encryptedNotes: string): string {
    return decrypt(encryptedNotes);
  }
  
  // Encrypt phone numbers (for privacy)
  static encryptPhone(phone: string): string {
    return encrypt(phone);
  }
  
  // Decrypt phone numbers
  static decryptPhone(encryptedPhone: string): string {
    return decrypt(encryptedPhone);
  }
  
  // Encrypt email addresses
  static encryptEmail(email: string): string {
    return encrypt(email);
  }
  
  // Decrypt email addresses
  static decryptEmail(encryptedEmail: string): string {
    return decrypt(encryptedEmail);
  }
}

// Database encryption helpers
export class DatabaseEncryption {
  // Encrypt patient data before saving
  static encryptPatientData(patientData: any): any {
    const encrypted = { ...patientData };
    
    // Encrypt sensitive fields
    if (encrypted.medical_history) {
      encrypted.medical_history = FieldEncryption.encryptMedicalHistory(encrypted.medical_history);
    }
    
    if (encrypted.dental_history) {
      encrypted.dental_history = FieldEncryption.encryptMedicalHistory(encrypted.dental_history);
    }
    
    if (encrypted.notes) {
      encrypted.notes = FieldEncryption.encryptNotes(encrypted.notes);
    }
    
    // Optionally encrypt contact information
    if (process.env.ENCRYPT_CONTACT_INFO === 'true') {
      if (encrypted.phone) {
        encrypted.phone = FieldEncryption.encryptPhone(encrypted.phone);
      }
      
      if (encrypted.email) {
        encrypted.email = FieldEncryption.encryptEmail(encrypted.email);
      }
    }
    
    return encrypted;
  }
  
  // Decrypt patient data after retrieval
  static decryptPatientData(encryptedData: any): any {
    const decrypted = { ...encryptedData };
    
    try {
      // Decrypt sensitive fields
      if (decrypted.medical_history && typeof decrypted.medical_history === 'string') {
        decrypted.medical_history = FieldEncryption.decryptMedicalHistory(decrypted.medical_history);
      }
      
      if (decrypted.dental_history && typeof decrypted.dental_history === 'string') {
        decrypted.dental_history = FieldEncryption.decryptMedicalHistory(decrypted.dental_history);
      }
      
      if (decrypted.notes && typeof decrypted.notes === 'string') {
        decrypted.notes = FieldEncryption.decryptNotes(decrypted.notes);
      }
      
      // Decrypt contact information if encrypted
      if (process.env.ENCRYPT_CONTACT_INFO === 'true') {
        if (decrypted.phone && typeof decrypted.phone === 'string') {
          decrypted.phone = FieldEncryption.decryptPhone(decrypted.phone);
        }
        
        if (decrypted.email && typeof decrypted.email === 'string') {
          decrypted.email = FieldEncryption.decryptEmail(decrypted.email);
        }
      }
    } catch (error) {
      console.error('Error decrypting patient data:', error);
      // Return original data if decryption fails (for backward compatibility)
    }
    
    return decrypted;
  }
  
  // Encrypt treatment notes
  static encryptTreatmentData(treatmentData: any): any {
    const encrypted = { ...treatmentData };
    
    if (encrypted.notes) {
      encrypted.notes = FieldEncryption.encryptNotes(encrypted.notes);
    }
    
    if (encrypted.diagnosis) {
      encrypted.diagnosis = FieldEncryption.encryptNotes(encrypted.diagnosis);
    }
    
    if (encrypted.treatment_plan) {
      encrypted.treatment_plan = FieldEncryption.encryptNotes(encrypted.treatment_plan);
    }
    
    return encrypted;
  }
  
  // Decrypt treatment notes
  static decryptTreatmentData(encryptedData: any): any {
    const decrypted = { ...encryptedData };
    
    try {
      if (decrypted.notes && typeof decrypted.notes === 'string') {
        decrypted.notes = FieldEncryption.decryptNotes(decrypted.notes);
      }
      
      if (decrypted.diagnosis && typeof decrypted.diagnosis === 'string') {
        decrypted.diagnosis = FieldEncryption.decryptNotes(decrypted.diagnosis);
      }
      
      if (decrypted.treatment_plan && typeof decrypted.treatment_plan === 'string') {
        decrypted.treatment_plan = FieldEncryption.decryptNotes(decrypted.treatment_plan);
      }
    } catch (error) {
      console.error('Error decrypting treatment data:', error);
    }
    
    return decrypted;
  }
}

// File encryption for uploaded documents
export class FileEncryption {
  // Encrypt file buffer
  static encryptFile(fileBuffer: Buffer): { encryptedData: Buffer; key: string; iv: string } {
    const key = crypto.randomBytes(KEY_LENGTH);
    const iv = crypto.randomBytes(IV_LENGTH);
    
    const cipher = crypto.createCipher(ENCRYPTION_ALGORITHM, key);
    
    const encrypted = Buffer.concat([
      cipher.update(fileBuffer),
      cipher.final()
    ]);
    
    const tag = cipher.getAuthTag();
    const encryptedData = Buffer.concat([encrypted, tag]);
    
    return {
      encryptedData,
      key: key.toString('hex'),
      iv: iv.toString('hex')
    };
  }
  
  // Decrypt file buffer
  static decryptFile(encryptedData: Buffer, key: string, iv: string): Buffer {
    const keyBuffer = Buffer.from(key, 'hex');
    const ivBuffer = Buffer.from(iv, 'hex');
    
    const tag = encryptedData.slice(-TAG_LENGTH);
    const encrypted = encryptedData.slice(0, -TAG_LENGTH);
    
    const decipher = crypto.createDecipher(ENCRYPTION_ALGORITHM, keyBuffer);
    decipher.setAuthTag(tag);
    
    return Buffer.concat([
      decipher.update(encrypted),
      decipher.final()
    ]);
  }
}

// Audit logging for encryption operations
export function logEncryptionOperation(operation: string, userId: string, resourceType: string, resourceId: string) {
  // Log encryption/decryption operations for compliance
  console.log(`[ENCRYPTION] ${operation} - User: ${userId}, Resource: ${resourceType}:${resourceId}, Time: ${new Date().toISOString()}`);
  
  // In production, this should be sent to a secure audit log system
  // that cannot be tampered with by application code
}

// Key rotation utilities
export class KeyRotation {
  // Generate new encryption key
  static generateNewKey(): string {
    return generateEncryptionKey();
  }
  
  // Re-encrypt data with new key
  static async reencryptData(oldEncryptedData: string, newKey: string): Promise<string> {
    // Decrypt with old key
    const plaintext = decrypt(oldEncryptedData);
    
    // Temporarily set new key
    const oldKey = process.env.ENCRYPTION_KEY;
    process.env.ENCRYPTION_KEY = newKey;
    
    try {
      // Encrypt with new key
      const newEncryptedData = encrypt(plaintext);
      return newEncryptedData;
    } finally {
      // Restore old key
      process.env.ENCRYPTION_KEY = oldKey;
    }
  }
  
  // Rotate encryption keys for all sensitive data
  static async rotateAllKeys(): Promise<void> {
    // This would be a complex operation that should be done during maintenance windows
    // 1. Generate new key
    // 2. Re-encrypt all sensitive data
    // 3. Update key in secure storage
    // 4. Verify all data can be decrypted with new key
    throw new Error('Key rotation not implemented - requires maintenance window');
  }
}

export default {
  encrypt,
  decrypt,
  hashPassword,
  verifyPassword,
  generateSecureToken,
  generateSecureQRCode,
  verifySecureQRCode,
  FieldEncryption,
  DatabaseEncryption,
  FileEncryption,
  KeyRotation,
  logEncryptionOperation
};
