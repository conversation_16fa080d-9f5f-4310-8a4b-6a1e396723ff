import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase-server';

export async function POST(request: NextRequest) {
  try {
    const { userId, code } = await request.json();

    if (!userId || !code) {
      return NextResponse.json(
        { error: 'User ID and verification code are required' },
        { status: 400 }
      );
    }

    // Validate code format (6 digits)
    if (!/^\d{6}$/.test(code)) {
      return NextResponse.json(
        { error: 'Invalid verification code format' },
        { status: 400 }
      );
    }

    const supabase = await createServerSupabaseClient();

    // Get the verification code from database
    const { data: verificationData, error: fetchError } = await supabase
      .from('two_factor_codes')
      .select('*')
      .eq('user_id', userId)
      .eq('verification_code', code)
      .eq('is_verified', false)
      .single();

    if (fetchError || !verificationData) {
      return NextResponse.json(
        { error: 'Invalid or expired verification code' },
        { status: 400 }
      );
    }

    // Check if code has expired
    const now = new Date();
    const expiresAt = new Date(verificationData.expires_at);
    
    if (now > expiresAt) {
      // Delete expired code
      await supabase
        .from('two_factor_codes')
        .delete()
        .eq('id', verificationData.id);

      return NextResponse.json(
        { error: 'Verification code has expired' },
        { status: 400 }
      );
    }

    // Mark code as verified
    const { error: updateError } = await supabase
      .from('two_factor_codes')
      .update({
        is_verified: true,
        verified_at: new Date().toISOString()
      })
      .eq('id', verificationData.id);

    if (updateError) {
      console.error('Database update error:', updateError);
      return NextResponse.json(
        { error: 'Failed to verify code' },
        { status: 500 }
      );
    }

    // Enable 2FA for the user
    const { error: enable2FAError } = await supabase
      .from('user_2fa_settings')
      .upsert({
        user_id: userId,
        phone_number: verificationData.phone_number,
        is_enabled: true,
        enabled_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (enable2FAError) {
      console.error('2FA enable error:', enable2FAError);
      return NextResponse.json(
        { error: 'Failed to enable 2FA' },
        { status: 500 }
      );
    }

    // Clean up used verification code
    await supabase
      .from('two_factor_codes')
      .delete()
      .eq('id', verificationData.id);

    return NextResponse.json({
      success: true,
      enabled: true,
      message: 'Two-factor authentication enabled successfully'
    });

  } catch (error) {
    console.error('2FA verify error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
