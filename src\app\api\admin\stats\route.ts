import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          },
        },
      }
    );

    // Get current user and verify super admin role
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is super admin (using your actual platform_admins table)
    // Use service role to bypass RLS for admin verification
    const adminSupabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        cookies: {
          getAll() { return []; },
          setAll() {},
        },
      }
    );

    const { data: adminData, error: roleError } = await adminSupabase
      .from('platform_admins')
      .select('id, role')
      .eq('id', user.id)
      .single();

    if (roleError || !adminData) {
      return NextResponse.json(
        { error: 'Forbidden - Super Admin access required' },
        { status: 403 }
      );
    }

    console.log('🔍 Fetching REAL platform statistics from your Supabase...');

    // Get real statistics from your actual database tables
    const [
      doctorsResult,
      patientsResult,
      appointmentsResult,
      invoicesResult
    ] = await Promise.all([
      // Total doctors from your doctors table
      supabase
        .from('doctors')
        .select('id, created_at, subscription_status, full_name, clinic_name')
        .order('created_at', { ascending: false }),

      // Total patients from your patients table
      supabase
        .from('patients')
        .select('id, created_at')
        .order('created_at', { ascending: false }),

      // Total appointments from your appointments table
      supabase
        .from('appointments')
        .select('id, created_at, status')
        .order('created_at', { ascending: false }),

      // Revenue data from your invoices table
      supabase
        .from('invoices')
        .select('total_amount, created_at, status')
        .eq('status', 'paid')
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
    ]);

    // Calculate statistics from your REAL Supabase data
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    // Doctors stats from your doctors table
    const totalDoctors = doctorsResult.data?.length || 0;
    const newDoctorsThisMonth = doctorsResult.data?.filter(d =>
      new Date(d.created_at) >= thirtyDaysAgo
    ).length || 0;
    const activeTrials = doctorsResult.data?.filter(d => d.subscription_status === 'trial').length || 0;

    // Patients stats from your patients table
    const totalPatients = patientsResult.data?.length || 0;
    const newPatientsThisWeek = patientsResult.data?.filter(p =>
      new Date(p.created_at) >= sevenDaysAgo
    ).length || 0;

    // Appointments stats from your appointments table
    const totalAppointments = appointmentsResult.data?.length || 0;
    const todayAppointments = appointmentsResult.data?.filter(a => {
      const appointmentDate = new Date(a.created_at);
      return appointmentDate.toDateString() === now.toDateString();
    }).length || 0;

    // Revenue stats from your invoices table
    const monthlyRevenue = invoicesResult.data?.reduce((sum, invoice) => {
      return sum + (invoice.total_amount || 0);
    }, 0) || 0;

    // Recent doctors (real data from your database)
    const recentDoctors = doctorsResult.data?.slice(0, 5).map(doctor => ({
      name: doctor.full_name,
      clinic: doctor.clinic_name,
      joinedDate: doctor.created_at,
      status: doctor.subscription_status
    })) || [];

    const stats = {
      totalDoctors,
      totalPatients,
      totalSubscriptions: totalDoctors, // Using doctors count as subscription count
      monthlyRevenue,
      activeTrials,
      totalAppointments,
      newDoctorsThisMonth,
      newPatientsThisWeek,
      todayAppointments,
      recentDoctors,
      lastUpdated: new Date().toISOString()
    };

    console.log('📊 Real platform stats from your Supabase:', stats);

    return NextResponse.json(stats);

  } catch (error) {
    console.error('Super Admin stats error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
