import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const slug = searchParams.get('slug');

    if (!slug) {
      return NextResponse.json(
        { error: 'Clinic slug is required' },
        { status: 400 }
      );
    }

    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          },
        },
      }
    );

    // Get doctor by clinic slug
    const { data: doctor, error: doctorError } = await supabase
      .from('doctors')
      .select(`
        id,
        full_name,
        clinic_name,
        clinic_slug,
        phone,
        whatsapp,
        email
      `)
      .eq('clinic_slug', slug)
      .eq('is_active', true)
      .single();

    if (doctorError || !doctor) {
      return NextResponse.json(
        { error: 'Clinic not found' },
        { status: 404 }
      );
    }

    // Get clinic settings
    const { data: clinicSettings } = await supabase
      .from('clinic_settings')
      .select(`
        working_hours,
        appointment_duration,
        allow_online_booking,
        timezone,
        phone,
        whatsapp
      `)
      .eq('doctor_id', doctor.id)
      .single();

    // Return clinic information for public booking
    return NextResponse.json({
      name: doctor.clinic_name,
      slug: doctor.clinic_slug,
      doctor: doctor.full_name,
      phone: clinicSettings?.phone || doctor.phone,
      whatsapp: clinicSettings?.whatsapp || doctor.whatsapp,
      email: doctor.email,
      allowOnlineBooking: clinicSettings?.allow_online_booking ?? true,
      workingHours: clinicSettings?.working_hours || {},
      appointmentDuration: clinicSettings?.appointment_duration || 30,
      timezone: clinicSettings?.timezone || 'UTC'
    });

  } catch (error) {
    console.error('Clinic info error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
