import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          },
        },
      }
    );

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Verify user is a doctor
    const { data: doctor, error: doctorError } = await supabase
      .from('doctors')
      .select('id')
      .eq('id', user.id)
      .single();

    if (doctorError || !doctor) {
      return NextResponse.json(
        { error: 'Doctor not found' },
        { status: 404 }
      );
    }

    // Get total patients count
    const { count: totalPatients } = await supabase
      .from('patients')
      .select('*', { count: 'exact', head: true })
      .eq('doctor_id', user.id);

    // Get today's appointments
    const today = new Date().toISOString().split('T')[0];
    const { count: todayAppointments } = await supabase
      .from('appointments')
      .select('*', { count: 'exact', head: true })
      .eq('doctor_id', user.id)
      .eq('appointment_date', today);

    // Get this week's appointments
    const startOfWeek = new Date();
    startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay());
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);

    const { count: thisWeekAppointments } = await supabase
      .from('appointments')
      .select('*', { count: 'exact', head: true })
      .eq('doctor_id', user.id)
      .gte('appointment_date', startOfWeek.toISOString().split('T')[0])
      .lte('appointment_date', endOfWeek.toISOString().split('T')[0]);

    // Get pending appointments
    const { count: pendingAppointments } = await supabase
      .from('appointments')
      .select('*', { count: 'exact', head: true })
      .eq('doctor_id', user.id)
      .eq('status', 'pending');

    // Get recent appointments for the dashboard
    const { data: recentAppointments } = await supabase
      .from('appointments')
      .select(`
        id,
        appointment_date,
        appointment_time,
        treatment_type,
        status,
        patients!inner(
          full_name,
          phone
        )
      `)
      .eq('doctor_id', user.id)
      .gte('appointment_date', today)
      .order('appointment_date', { ascending: true })
      .order('appointment_time', { ascending: true })
      .limit(5);

    // Get low stock materials
    const { data: lowStockMaterials } = await supabase
      .from('materials')
      .select('id, name, current_stock, minimum_stock, unit')
      .eq('doctor_id', user.id)
      .eq('is_active', true)
      .filter('current_stock', 'lte', 'minimum_stock')
      .limit(5);

    // Calculate revenue for this month
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    const { data: monthlyInvoices } = await supabase
      .from('invoices')
      .select('total_amount')
      .eq('doctor_id', user.id)
      .eq('status', 'paid')
      .gte('created_at', startOfMonth.toISOString());

    const monthlyRevenue = monthlyInvoices?.reduce((sum, invoice) => sum + (invoice.total_amount || 0), 0) || 0;

    const stats = {
      totalPatients: totalPatients || 0,
      todayAppointments: todayAppointments || 0,
      thisWeekAppointments: thisWeekAppointments || 0,
      pendingAppointments: pendingAppointments || 0,
      monthlyRevenue: monthlyRevenue,
      recentAppointments: recentAppointments?.map(apt => ({
        id: apt.id,
        patientName: apt.patients?.full_name,
        date: apt.appointment_date,
        time: apt.appointment_time,
        type: apt.treatment_type,
        status: apt.status,
        phone: apt.patients?.phone
      })) || [],
      lowStockMaterials: lowStockMaterials?.map(material => ({
        id: material.id,
        name: material.name,
        currentStock: material.current_stock,
        minimumStock: material.minimum_stock,
        unit: material.unit
      })) || []
    };

    return NextResponse.json(stats);

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
