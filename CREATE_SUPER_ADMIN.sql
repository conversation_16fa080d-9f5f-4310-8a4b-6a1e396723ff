-- =============================================
-- CREATE SUPER ADMIN FOR DENTOPRO SAAS
-- =============================================
-- Run this in Supabase SQL Editor to create your first super admin
-- REPLACE the email and details with YOUR ACTUAL INFORMATION

-- =============================================
-- 1. FIRST: CREATE SUPABASE AUTH USER
-- =============================================
-- You MUST do this step manually in Supabase Dashboard:
-- 1. Go to Authentication → Users
-- 2. Click "Add User"
-- 3. Enter YOUR email and password
-- 4. Copy the generated User ID
-- 5. Use that ID in the script below

-- =============================================
-- 2. CREATE PLATFORM ADMIN RECORD
-- =============================================
-- REPLACE THESE VALUES WITH YOUR ACTUAL INFORMATION:

-- STEP 1: Replace this UUID with the actual User ID from Supabase Auth
-- Go to: Supabase Dashboard → Authentication → Users → Copy the ID
DO $$
DECLARE
    admin_user_id UUID := '00000000-0000-0000-0000-000000000000'; -- REPLACE WITH ACTUAL USER ID
    admin_email VARCHAR(255) := '<EMAIL>'; -- REPLACE WITH YOUR EMAIL
    admin_name VARCHAR(255) := 'Platform Administrator'; -- REPLACE WITH YOUR NAME
    admin_password_hash VARCHAR(255) := 'supabase_managed'; -- Supabase manages the password
BEGIN
    -- Check if admin already exists
    IF NOT EXISTS (SELECT 1 FROM platform_admins WHERE email = admin_email) THEN
        -- Insert the super admin
        INSERT INTO platform_admins (
            id,
            email,
            password_hash,
            full_name,
            role,
            permissions,
            is_active,
            created_at,
            updated_at
        ) VALUES (
            admin_user_id,
            admin_email,
            admin_password_hash,
            admin_name,
            'super_admin',
            '{
                "platform_management": true,
                "doctor_approval": true,
                "subscription_management": true,
                "analytics_access": true,
                "system_settings": true,
                "user_management": true,
                "billing_access": true,
                "support_access": true
            }'::jsonb,
            true,
            NOW(),
            NOW()
        );
        
        RAISE NOTICE '✅ Super Admin created successfully!';
        RAISE NOTICE 'Email: %', admin_email;
        RAISE NOTICE 'Name: %', admin_name;
        RAISE NOTICE 'Role: super_admin';
    ELSE
        RAISE NOTICE '⚠️ Admin with email % already exists', admin_email;
    END IF;
END $$;

-- =============================================
-- 3. VERIFY ADMIN CREATION
-- =============================================
SELECT 
    id,
    email,
    full_name,
    role,
    permissions,
    is_active,
    created_at
FROM platform_admins
WHERE role = 'super_admin'
ORDER BY created_at DESC;

-- =============================================
-- 4. ENABLE RLS POLICIES FOR ADMIN ACCESS
-- =============================================

-- Enable RLS on platform_admins table
ALTER TABLE platform_admins ENABLE ROW LEVEL SECURITY;

-- Policy for super admins to access all admin data
CREATE POLICY IF NOT EXISTS "Super admins can access all admin data" ON platform_admins
    FOR ALL USING (
        auth.uid() IN (SELECT id FROM platform_admins WHERE role = 'super_admin' AND is_active = true)
    );

-- Policy for admins to see their own data
CREATE POLICY IF NOT EXISTS "Admins can see own data" ON platform_admins
    FOR SELECT USING (auth.uid() = id);

-- =============================================
-- 5. CREATE ADMIN HELPER FUNCTIONS
-- =============================================

-- Function to check if current user is super admin
CREATE OR REPLACE FUNCTION is_super_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM platform_admins 
        WHERE id = auth.uid() 
        AND role = 'super_admin' 
        AND is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get admin permissions
CREATE OR REPLACE FUNCTION get_admin_permissions()
RETURNS JSONB AS $$
DECLARE
    admin_perms JSONB;
BEGIN
    SELECT permissions INTO admin_perms
    FROM platform_admins 
    WHERE id = auth.uid() 
    AND is_active = true;
    
    RETURN COALESCE(admin_perms, '{}'::jsonb);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- 6. UPDATE AUTH METADATA FOR ADMIN USER
-- =============================================

-- This function updates the Supabase Auth user metadata
-- Run this after creating the admin record
CREATE OR REPLACE FUNCTION update_admin_auth_metadata(admin_user_id UUID)
RETURNS TEXT AS $$
BEGIN
    -- Note: This requires service role key to work
    -- You may need to run this manually in Supabase Dashboard
    RETURN 'Please update user metadata manually in Supabase Dashboard:
    1. Go to Authentication → Users
    2. Find user: ' || admin_user_id::text || '
    3. Click Edit User
    4. Add to User Metadata:
    {
        "role": "super_admin",
        "permissions": {
            "platform_management": true,
            "doctor_approval": true,
            "subscription_management": true
        }
    }';
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- 7. VERIFICATION AND NEXT STEPS
-- =============================================

DO $$
DECLARE
    admin_count INTEGER;
    admin_email VARCHAR(255);
BEGIN
    -- Count super admins
    SELECT COUNT(*), MAX(email) INTO admin_count, admin_email
    FROM platform_admins 
    WHERE role = 'super_admin' AND is_active = true;
    
    RAISE NOTICE '=== SUPER ADMIN SETUP VERIFICATION ===';
    
    IF admin_count > 0 THEN
        RAISE NOTICE '✅ Super Admin Setup Complete!';
        RAISE NOTICE 'Total Super Admins: %', admin_count;
        RAISE NOTICE 'Latest Admin Email: %', admin_email;
        RAISE NOTICE '';
        RAISE NOTICE '🔗 NEXT STEPS:';
        RAISE NOTICE '1. Go to: http://localhost:3000/auth/login';
        RAISE NOTICE '2. Login with: %', admin_email;
        RAISE NOTICE '3. Should redirect to: /admin';
        RAISE NOTICE '4. Update user metadata in Supabase Dashboard';
        RAISE NOTICE '';
        RAISE NOTICE '📋 MANUAL STEPS REQUIRED:';
        RAISE NOTICE '1. Supabase Dashboard → Authentication → Users';
        RAISE NOTICE '2. Find your user and click Edit';
        RAISE NOTICE '3. Add to User Metadata: {"role": "super_admin"}';
    ELSE
        RAISE NOTICE '❌ No Super Admin Found!';
        RAISE NOTICE 'Please check the admin_user_id in the script';
        RAISE NOTICE 'Make sure you created the Supabase Auth user first';
    END IF;
    
    RAISE NOTICE '=== END VERIFICATION ===';
END $$;
