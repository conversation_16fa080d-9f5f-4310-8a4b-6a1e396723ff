import Stripe from 'stripe';
import { loadStripe } from '@stripe/stripe-js';

// Server-side Stripe instance
export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
  typescript: true,
});

// Client-side Stripe instance
let stripePromise: Promise<Stripe | null>;

export const getStripe = () => {
  if (!stripePromise) {
    stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);
  }
  return stripePromise;
};

// Subscription plan configurations
export const SUBSCRIPTION_PLANS = {
  BASIC: {
    id: 'plan_basic',
    name: 'Basic Plan',
    price: 9.00,
    priceId: 'price_basic_monthly', // This will be created in Stripe
    features: [
      'Up to 100 patients',
      'Basic appointment scheduling',
      'Patient records management',
      'Email support',
      'Mobile app access',
      'Basic reporting'
    ]
  },
  PREMIUM: {
    id: 'plan_premium',
    name: 'Premium Plan',
    price: 15.00,
    priceId: 'price_premium_monthly', // This will be created in Stripe
    features: [
      'Unlimited patients',
      'Advanced appointment scheduling',
      'Complete patient management',
      'WhatsApp integration',
      'Priority support',
      'Advanced analytics',
      'Inventory management',
      'Multi-location support',
      'Custom branding'
    ]
  },
  TRIAL: {
    id: 'plan_trial',
    name: '7-Day Free Trial',
    price: 0.00,
    priceId: null,
    features: [
      'All Premium features',
      '7-day free trial',
      'No credit card required'
    ]
  }
} as const;

// Helper functions
export const formatPrice = (price: number, currency: string = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(price);
};

export const calculateTrialEndDate = (startDate: Date = new Date()) => {
  const trialEnd = new Date(startDate);
  trialEnd.setDate(trialEnd.getDate() + 7);
  return trialEnd;
};

export const isTrialExpired = (trialEndDate: Date) => {
  return new Date() > trialEndDate;
};

// Stripe webhook event types we handle
export const STRIPE_WEBHOOK_EVENTS = {
  PAYMENT_SUCCEEDED: 'invoice.payment_succeeded',
  PAYMENT_FAILED: 'invoice.payment_failed',
  SUBSCRIPTION_CREATED: 'customer.subscription.created',
  SUBSCRIPTION_UPDATED: 'customer.subscription.updated',
  SUBSCRIPTION_DELETED: 'customer.subscription.deleted',
  CUSTOMER_CREATED: 'customer.created',
} as const;
