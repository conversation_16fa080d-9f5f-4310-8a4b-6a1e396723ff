import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { Database } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    console.log('🧹 Clear admin API called');

    // Create Supabase client with service role
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        cookies: {
          getAll() { return []; },
          setAll() {},
        },
      }
    );

    // Delete all admin records
    const { error: deleteError } = await supabase
      .from('platform_admins')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all records

    if (deleteError) {
      console.error('Error deleting admin records:', deleteError);
      return NextResponse.json(
        { error: 'Failed to clear admin data' },
        { status: 500 }
      );
    }

    console.log('✅ Admin data cleared successfully');

    return NextResponse.json({
      success: true,
      message: 'Admin data cleared successfully'
    });

  } catch (error) {
    console.error('❌ Clear admin error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
