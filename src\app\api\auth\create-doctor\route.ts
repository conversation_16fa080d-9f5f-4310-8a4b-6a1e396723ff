import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Use service role to bypass RLS
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!, // Service role key bypasses RLS
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export async function POST(request: NextRequest) {
  try {
    const doctorData = await request.json();
    
    console.log('🔍 API: Creating doctor record with data:', doctorData);

    // Validate required fields
    if (!doctorData.id || !doctorData.email || !doctorData.full_name) {
      return NextResponse.json(
        { error: 'Missing required fields: id, email, full_name' },
        { status: 400 }
      );
    }

    // Create doctor record using service role (bypasses RLS)
    const { data: doctorRecord, error: doctorError } = await supabaseAdmin
      .from('doctors')
      .insert(doctorData)
      .select()
      .single();

    if (doctorError) {
      console.error('❌ API: Failed to create doctor record:', doctorError);
      return NextResponse.json(
        { 
          error: 'Failed to create doctor record',
          details: doctorError.message,
          code: doctorError.code
        },
        { status: 500 }
      );
    }

    console.log('✅ API: Doctor record created successfully:', doctorRecord);

    return NextResponse.json({
      success: true,
      doctor: doctorRecord,
      message: 'Doctor record created successfully'
    });

  } catch (error) {
    console.error('❌ API: Error in create-doctor endpoint:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
