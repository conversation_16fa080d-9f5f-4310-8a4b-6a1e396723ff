import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');

    if (!date) {
      return NextResponse.json(
        { error: 'Date parameter is required' },
        { status: 400 }
      );
    }

    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          },
        },
      }
    );

    // Get THE doctor who owns this installation
    const { data: doctor, error: doctorError } = await supabase
      .from('doctors')
      .select('id, clinic_name, full_name')
      .eq('is_active', true)
      .single();

    if (doctorError || !doctor) {
      return NextResponse.json(
        { error: 'Doctor not available' },
        { status: 400 }
      );
    }

    // Get doctor's clinic settings
    const { data: clinicSettings } = await supabase
      .from('clinic_settings')
      .select('working_hours, appointment_duration, buffer_time')
      .eq('doctor_id', doctor.id)
      .single();

    if (!clinicSettings) {
      return NextResponse.json(
        { error: 'Clinic settings not configured' },
        { status: 400 }
      );
    }

    // Get day of week for the requested date
    const requestedDate = new Date(date);
    const dayOfWeek = requestedDate.toLocaleDateString('en-US', { weekday: 'lowercase' });
    
    // Check if doctor works on this day
    const daySchedule = clinicSettings.working_hours?.[dayOfWeek];
    if (!daySchedule?.enabled) {
      return NextResponse.json({
        available_slots: [],
        message: 'Doctor is not available on this day'
      });
    }

    // Generate time slots based on working hours
    const startTime = daySchedule.start || '09:00';
    const endTime = daySchedule.end || '17:00';
    const appointmentDuration = clinicSettings.appointment_duration || 30;
    const bufferTime = clinicSettings.buffer_time || 15;
    
    const slots = generateTimeSlots(startTime, endTime, appointmentDuration + bufferTime);

    // Get existing appointments for this date
    const { data: existingAppointments } = await supabase
      .from('appointments')
      .select('appointment_time')
      .eq('doctor_id', doctor.id)
      .eq('appointment_date', date)
      .neq('status', 'cancelled');

    // Filter out booked slots
    const bookedTimes = existingAppointments?.map(apt => apt.appointment_time) || [];
    const availableSlots = slots.filter(slot => !bookedTimes.includes(slot));

    // Don't allow booking in the past
    const now = new Date();
    const today = now.toISOString().split('T')[0];
    const currentTime = now.toTimeString().slice(0, 5);
    
    const filteredSlots = date === today 
      ? availableSlots.filter(slot => slot > currentTime)
      : availableSlots;

    return NextResponse.json({
      available_slots: filteredSlots,
      doctor_name: doctor.full_name,
      clinic_name: doctor.clinic_name,
      date: date,
      day_of_week: dayOfWeek
    });

  } catch (error) {
    console.error('Available slots error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper function to generate time slots
function generateTimeSlots(startTime: string, endTime: string, intervalMinutes: number): string[] {
  const slots: string[] = [];
  const start = new Date(`2000-01-01T${startTime}:00`);
  const end = new Date(`2000-01-01T${endTime}:00`);
  
  let current = new Date(start);
  
  while (current < end) {
    slots.push(current.toTimeString().slice(0, 5));
    current.setMinutes(current.getMinutes() + intervalMinutes);
  }
  
  return slots;
}
